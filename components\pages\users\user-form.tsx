"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { Input } from "@/components/ui/input";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { ACCEPTED_IMAGE_TYPES, MAX_FILE_SIZE } from "@/lib/constants";
import { createUser, updateUser } from "@/lib/firebase/users/actions";
import { getUserById } from "@/lib/firebase/users/service";
import { formatBytes } from "@/lib/utils";
import { userSchema } from "@/schemas/user-form-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useEffect, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

export type UserFormType = z.infer<typeof userSchema>;

function UserForm() {
   const searchParams = useSearchParams();
   const id = searchParams.get("id");

   const [pending, startTransition] = useTransition();
   const [error, setError] = useState({ message: "" });
   const [image, setImage] = useState({
      prevImage: "",
      newImage: "" as string | File | undefined,
   });
   const [dataLoaded, setDataLoaded] = useState<boolean | undefined>(
      id ? false : undefined,
   );

   const form = useForm<z.infer<typeof userSchema>>({
      resolver: zodResolver(userSchema),
      defaultValues: {
         name: "",
         email: "",
         password: "",
         confirmPassword: "",
         role: "user",
         image: undefined,
         isNewUser: !id, // Set isNewUser based on whether we have an ID
      },
   });

   async function onSubmit(values: z.infer<typeof userSchema>) {
      startTransition(async () => {
         // Add image to form data if it exists
         if (image.newImage) {
            values.image = image.newImage;
         }

         const result = id
            ? await updateUser(values, id)
            : await createUser(values);

         if (!result.success) {
            setError({ message: result.message });
            return;
         }

         toast.success(result.message);
         form.reset();
      });
   }

   // Load user data if editing
   useEffect(() => {
      async function getUserData() {
         if (id) {
            try {
               const userData = await getUserById(id);
               if (userData) {
                  form.reset({
                     name: userData.name || "",
                     email: userData.email || "",
                     role:
                        (userData.role as "user" | "admin" | "editor") ||
                        "user",
                     password: "", // Empty password field when editing
                     confirmPassword: "", // Empty confirm password field when editing
                     isNewUser: false, // This is an existing user
                  });

                  if (userData.image) {
                     setImage({
                        prevImage: userData.image,
                        newImage: userData.image,
                     });
                  }
                  setDataLoaded(true);
               } else {
                  setDataLoaded(false);
               }
            } catch (error) {
               console.error("Error loading user:", error);
               toast.error("Error loading user data");
               setDataLoaded(false);
            }
         }
      }
      getUserData();
   }, [id, form]);

   // Handle image change
   function handleImageChange(e: React.ChangeEvent<HTMLInputElement>) {
      const file = e.target.files?.[0];
      if (!file) return;

      if (file.size > MAX_FILE_SIZE) {
         toast.error(
            `File is too large. Maximum size is ${formatBytes(MAX_FILE_SIZE)}.`,
         );
         return;
      }

      if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
         toast.error(
            "File type not supported. Please upload a JPEG, PNG, or WebP image.",
         );
         return;
      }

      setImage({
         ...image,
         newImage: file,
      });

      // Preview the image
      const reader = new FileReader();
      reader.onloadend = () => {
         setImage({
            prevImage: reader.result as string,
            newImage: file,
         });
      };
      reader.readAsDataURL(file);
   }

   return (
      <FormWrapper
         isSubmitting={pending}
         isDataLoaded={dataLoaded}
         loadingMessage="Loading user form..."
         editingMessage="Loading user data..."
         submittingMessage="Saving user..."
      >
         <div className="space-y-6">
            <div>
               <h1 className="text-3xl font-bold">
                  {id ? "Edit User" : "Create New User"}
               </h1>
               <p className="text-gray-500 dark:text-gray-400">
                  {id
                     ? "Update user information"
                     : "Fill in the form below to create a new user"}
               </p>
            </div>

            {error.message && (
               <div className="rounded-md bg-destructive/15 p-4 text-destructive">
                  {error.message}
               </div>
            )}

            <Form {...form}>
               <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
               >
                  <div className="grid gap-4 md:grid-cols-2">
                     <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>Display Name</FormLabel>
                              <FormControl>
                                 <Input placeholder="Display Name" {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />

                     <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>Email</FormLabel>
                              <FormControl>
                                 <Input
                                    placeholder="Email"
                                    type="email"
                                    {...field}
                                 />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  {/* Hidden field for isNewUser */}
                  <input type="hidden" {...form.register("isNewUser")} />

                  <div className="grid gap-4 md:grid-cols-2">
                     <FormField
                        control={form.control}
                        name="password"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>
                                 {id ? "New Password (optional)" : "Password"}
                              </FormLabel>
                              <FormControl>
                                 <Input
                                    placeholder={
                                       id
                                          ? "Leave blank to keep current"
                                          : "Password"
                                    }
                                    type="password"
                                    {...field}
                                 />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />

                     <FormField
                        control={form.control}
                        name="confirmPassword"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>
                                 {id
                                    ? "Confirm New Password"
                                    : "Confirm Password"}
                              </FormLabel>
                              <FormControl>
                                 <Input
                                    placeholder={
                                       id
                                          ? "Leave blank to keep current"
                                          : "Confirm Password"
                                    }
                                    type="password"
                                    {...field}
                                 />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                     <FormField
                        control={form.control}
                        name="role"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>Role</FormLabel>
                              <Select
                                 onValueChange={field.onChange}
                                 defaultValue={field.value}
                                 value={field.value}
                              >
                                 <FormControl>
                                    <SelectTrigger>
                                       <SelectValue placeholder="Select a role" />
                                    </SelectTrigger>
                                 </FormControl>
                                 <SelectContent>
                                    <SelectItem value="user">User</SelectItem>
                                    <SelectItem value="editor">
                                       Editor
                                    </SelectItem>
                                    <SelectItem value="admin">Admin</SelectItem>
                                 </SelectContent>
                              </Select>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  {/* Profile Image */}
                  <div className="space-y-4">
                     <h2 className="text-xl font-semibold">Profile Image</h2>

                     <div className="flex flex-col items-center space-y-4">
                        {image.prevImage && (
                           <div className="relative h-32 w-32 overflow-hidden rounded-full">
                              <Image
                                 src={image.prevImage}
                                 alt="Profile preview"
                                 fill
                                 className="object-cover"
                              />
                           </div>
                        )}

                        <Input
                           type="file"
                           accept="image/*"
                           onChange={handleImageChange}
                           className="max-w-sm"
                        />
                        <p className="text-xs text-gray-500">
                           Accepted formats: JPEG, PNG, WebP. Max size:{" "}
                           {formatBytes(MAX_FILE_SIZE)}
                        </p>
                     </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                     <Button
                        type="submit"
                        disabled={pending}
                        className="min-w-[120px]"
                     >
                        {pending ? (
                           <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              {id ? "Updating..." : "Creating..."}
                           </>
                        ) : id ? (
                           "Update User"
                        ) : (
                           "Create User"
                        )}
                     </Button>
                  </div>
               </form>
            </Form>
         </div>
      </FormWrapper>
   );
}

export default UserForm;
