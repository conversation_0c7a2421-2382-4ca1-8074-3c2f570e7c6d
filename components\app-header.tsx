"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuLabel,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
// Import sidebar constants
const SIDEBAR_WIDTH = "16rem";
const SIDEBAR_WIDTH_ICON = "3.5rem";

import { SidebarTrigger, useSidebar } from "@/components/ui/sidebar";
import { LogOut } from "lucide-react";
import { signOut, useSession } from "next-auth/react";
import { useTransition } from "react";

export function AppHeader() {
   const { data: session } = useSession();
   const [isPending, startTransition] = useTransition();
   const { state, isMobile } = useSidebar();

   const handleSignOut = () => {
      startTransition(async () => {
         await signOut({ callbackUrl: "/" });
      });
   };

   return (
      <header
         className="fixed right-0 top-0 z-50 flex h-16 shrink-0 items-center justify-between border-b bg-background px-4 md:px-6"
         style={{
            width: isMobile
               ? "100%"
               : state === "collapsed"
                 ? `calc(100% - var(--sidebar-width-icon, ${SIDEBAR_WIDTH_ICON}))`
                 : `calc(100% - var(--sidebar-width, ${SIDEBAR_WIDTH}))`,
         }}
      >
         <div className="flex items-center gap-2">
            <SidebarTrigger />
         </div>

         <div className="flex items-center gap-4">
            {session?.user && (
               <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                     <Button
                        variant="ghost"
                        className="relative h-10 w-10 rounded-full"
                     >
                        <Avatar className="h-9 w-9">
                           <AvatarImage
                              src={session.user.image || ""}
                              alt={session.user.name || "User"}
                           />
                           <AvatarFallback>
                              {session.user.name?.charAt(0)?.toUpperCase() ||
                                 "U"}
                           </AvatarFallback>
                        </Avatar>
                     </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                     <DropdownMenuLabel>
                        <div className="flex flex-col space-y-1">
                           <p className="text-sm font-medium leading-none">
                              {session.user.name}
                           </p>
                           <p className="text-xs leading-none text-muted-foreground">
                              {session.user.email}
                           </p>
                        </div>
                     </DropdownMenuLabel>
                     <DropdownMenuSeparator />
                     <DropdownMenuItem asChild>
                        <button
                           className="w-full cursor-pointer"
                           onClick={handleSignOut}
                           disabled={isPending}
                        >
                           {isPending ? (
                              <span className="flex items-center">
                                 <svg
                                    className="mr-2 h-4 w-4 animate-spin"
                                    viewBox="0 0 24 24"
                                 >
                                    <circle
                                       className="opacity-25"
                                       cx="12"
                                       cy="12"
                                       r="10"
                                       stroke="currentColor"
                                       strokeWidth="4"
                                    />
                                    <path
                                       className="opacity-75"
                                       fill="currentColor"
                                       d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                                    />
                                 </svg>
                                 Signing out...
                              </span>
                           ) : (
                              <span className="flex items-center">
                                 <LogOut className="mr-2 h-4 w-4" />
                                 Sign out
                              </span>
                           )}
                        </button>
                     </DropdownMenuItem>
                  </DropdownMenuContent>
               </DropdownMenu>
            )}
         </div>
      </header>
   );
}
