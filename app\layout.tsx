import type { <PERSON>ada<PERSON> } from "next";
import { SessionProvider } from "next-auth/react";
import { Toaster } from "sonner";
import "./globals.css";

export const metadata: Metadata = {
   title: "Pimpim Admin",
   description: "Pimpim Admin",
};

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <html lang="en">
         <body className={"h-svh antialiased"}>
            <SessionProvider>
               {children}
               <Toaster />
            </SessionProvider>
         </body>
      </html>
   );
}
