import { Button } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { DataTableWrapper } from "@/components/ui/data-table-wrapper";
import { getAuthors } from "@/lib/firebase/authors/service";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { columns } from "./table-columns";

// Separate component for the table content that will be wrapped with Suspense
async function AuthorsTableContent() {
   const authors = await getAuthors();

   return (
      <DataTable
         data={authors}
         columns={columns}
         defaultSortId="name"
         filterBy="name"
         customButton={
            <Link href="/authors/form">
               <Button className="ml-auto" variant="outline">
                  <PlusIcon
                     className="-ms-1 opacity-60"
                     size={16}
                     aria-hidden="true"
                  />
                  Add author
               </Button>
            </Link>
         }
      />
   );
}

async function AuthorsPage() {
   return (
      <>
         <h1 className="pb-4 text-2xl font-semibold">Authors Page</h1>
         <DataTableWrapper columnCount={5}>
            <AuthorsTableContent />
         </DataTableWrapper>
      </>
   );
}

export default AuthorsPage;
