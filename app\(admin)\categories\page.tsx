import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { DataTableWrapper } from "@/components/ui/data-table-wrapper";
import { getCategories } from "@/lib/firebase/categories/service";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { columns } from "./table-columns";

// Separate component for the table content that will be wrapped with Suspense
async function CategoriesTableContent() {
   const categories = await getCategories();

   return (
      <DataTable
         data={categories}
         columns={columns}
         defaultSortId="name"
         filterBy="name"
         customButton={
            <Link href="/categories/form">
               <Button className="ml-auto" variant="outline">
                  <PlusIcon
                     className="-ms-1 opacity-60"
                     size={16}
                     aria-hidden="true"
                  />
                  Add category
               </Button>
            </Link>
         }
      />
   );
}

async function CategoriesPage() {
   return (
      <>
         <h1 className="pb-4 text-2xl font-semibold">Categories Page</h1>
         <DataTableWrapper columnCount={5}>
            <CategoriesTableContent />
         </DataTableWrapper>
      </>
   );
}

export default CategoriesPage;
