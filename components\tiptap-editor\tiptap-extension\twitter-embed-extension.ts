import { TwitterEmbed } from "@/components/tiptap-editor/tiptap-node/twitter-embed-node/twitter-embed-node";
import { Node, mergeAttributes } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";

export interface TwitterEmbedOptions {
   HTMLAttributes: Record<string, string | number | boolean>;
}

declare module "@tiptap/core" {
   interface Commands<ReturnType> {
      twitterEmbed: {
         /**
          * Add a Twitter embed
          */
         setTwitterEmbed: (options: { src: string }) => ReturnType;
      };
   }
}

export const TwitterEmbedExtension = Node.create<TwitterEmbedOptions>({
   name: "twitterEmbed",

   group: "block",

   content: "",

   marks: "",

   atom: true,

   draggable: true,

   selectable: true,

   inline: false,

   addOptions() {
      return {
         HTMLAttributes: {
            class: "twitter-embed",
         },
      };
   },

   addAttributes() {
      return {
         src: {
            default: null,
         },
         width: {
            default: "100%",
         },
         theme: {
            default: "light",
         },
         title: {
            default: "Twitter post",
         },
      };
   },

   parseHTML() {
      return [
         {
            tag: 'div[data-type="twitter-embed"]',
            getAttrs: (node) => {
               const dom = node as HTMLElement;
               const blockquote = dom.querySelector("blockquote.twitter-tweet");
               if (blockquote) {
                  const link = blockquote.querySelector("a");
                  if (link) {
                     const src = link.getAttribute("href");
                     const width =
                        blockquote.getAttribute("data-width") || "100%";
                     const theme =
                        blockquote.getAttribute("data-theme") || "light";
                     return { src, width, theme };
                  }
               }
               return {};
            },
         },
         {
            tag: "blockquote.twitter-tweet",
            getAttrs: (node) => {
               const dom = node as HTMLElement;
               const link = dom.querySelector("a");
               if (link) {
                  const src = link.getAttribute("href");
                  const width = dom.getAttribute("data-width") || "100%";
                  const theme = dom.getAttribute("data-theme") || "light";
                  return { src, width, theme };
               }
               return false;
            },
         },
      ];
   },

   renderHTML({ HTMLAttributes }) {
      const attrs = mergeAttributes(
         this.options.HTMLAttributes,
         HTMLAttributes,
      );

      // Helper function to extract Twitter tweet ID from URL
      const getTweetId = (url: string | null) => {
         if (!url) return null;

         const regExp = /twitter.com\/[^/]+\/status\/(\d+)/;
         const match = url.match(regExp);

         // Also handle x.com URLs
         const xRegExp = /x.com\/[^/]+\/status\/(\d+)/;
         const xMatch = url.match(xRegExp);

         return match ? match[1] : xMatch ? xMatch[1] : null;
      };

      const tweetId = getTweetId(attrs.src);

      if (!tweetId) {
         return [
            "div",
            mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
               "data-type": "twitter-embed",
            }),
         ];
      }

      return [
         "div",
         { class: "twitter-embed", "data-type": "twitter-embed" },
         [
            "blockquote",
            {
               class: "twitter-tweet",
               "data-theme": attrs.theme || "light",
               "data-width": attrs.width || "100%",
            },
            ["a", { href: attrs.src }],
         ],
         [
            "script",
            {
               async: "true",
               src: "https://platform.twitter.com/widgets.js",
               charset: "utf-8",
            },
         ],
      ];
   },

   addNodeView() {
      return ReactNodeViewRenderer(TwitterEmbed);
   },

   addCommands() {
      return {
         setTwitterEmbed:
            (options) =>
            ({ commands }) => {
               return commands.insertContent({
                  type: this.name,
                  attrs: options,
               });
            },
      };
   },
});

export default TwitterEmbedExtension;
