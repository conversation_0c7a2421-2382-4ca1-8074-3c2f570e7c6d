"use client";

import { signOut, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { toast } from "sonner";
import Spinner from "../ui/spinner";

type RequireAuthProps = {
   children: React.ReactNode;
   requiredRoles?: string[];
};

export default function RequireAuth({
   children,
   requiredRoles = ["admin", "editor"],
}: RequireAuthProps) {
   const { data: session, status } = useSession();
   const router = useRouter();

   useEffect(() => {
      // If the user is not authenticated, redirect to login
      if (status === "unauthenticated") {
         toast.error("You must be logged in to access this page");
         router.push("/");
         return;
      }

      // If authentication is still loading, do nothing yet
      if (status === "loading") {
         return;
      }

      // If the user is authenticated but doesn't have the required role
      if (
         session &&
         session.user &&
         !requiredRoles.includes(session.user.role as string)
      ) {
         toast.error("You don't have permission to access this page");

         // Log the user out
         signOut({ callbackUrl: "/" });
      }
   }, [session, status, router, requiredRoles]);

   // Show nothing while checking authentication
   if (status === "loading") {
      return <Spinner />;
   }

   // If authenticated and has the required role, show the children
   if (
      status === "authenticated" &&
      session?.user &&
      requiredRoles.includes(session.user.role as string)
   ) {
      return <>{children}</>;
   }

   // Otherwise, show nothing (will be redirected by the useEffect)
   return null;
}
