import { ACCEPTED_IMAGE_TYPES, MAX_FILE_SIZE } from "@/lib/constants";
import { formatBytes } from "@/lib/utils";
import { z } from "zod";

export const socialLinksSchema = z.object({
   instagram: z.string().optional(),
   facebook: z.string().optional(),
   youtube: z.string().optional(),
   tiktok: z.string().optional(),
   twitter: z.string().optional(),
});

export const portfolioLinksSchema = z.object({
   behance: z.string().optional(),
   dribbble: z.string().optional(),
   linkedin: z.string().optional(),
   website: z.string().optional(),
});

export const userProfileSchema = z.object({
   firstName: z.string().optional(),
   lastName: z.string().optional(),
   username: z.string().optional(),
   phone: z.string().optional(),
   location: z.string().optional(),
   occupation: z.string().optional(),
   bio: z.string().optional(),
   dateOfBirth: z.string().optional(),
   socialLinks: socialLinksSchema.optional(),
   portfolioLinks: portfolioLinksSchema.optional(),
});

export const userSchema = z
   .object({
      name: z
         .string()
         .min(1, { message: "Name is required" })
         .min(3, { message: "Name must be at least 3 characters" }),
      email: z
         .string()
         .min(1, { message: "Email is required" })
         .email({ message: "Please enter a valid email address" }),
      password: z
         .string()
         .min(8, { message: "Password must be at least 8 characters" })
         .optional()
         .or(z.literal("")),
      confirmPassword: z
         .string()
         .min(8, { message: "Password must be at least 8 characters" })
         .optional()
         .or(z.literal("")),
      role: z
         .enum(["user", "admin", "editor"], {
            required_error: "Role is required",
         })
         .default("user"),
      image: z
         .union([
            z
               .instanceof(File, {
                  message: "Please select an image file.",
               })
               .refine((file) => file.size <= MAX_FILE_SIZE, {
                  message: `The image is too large. Please choose an image smaller than ${formatBytes(MAX_FILE_SIZE)}.`,
               })
               .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
                  message:
                     "Please upload a valid image file (JPEG, PNG, or WebP).",
               }),
            z.string().url({
               message: "Please provide a valid URL for the image.",
            }),
         ])
         .optional(),
      isNewUser: z.boolean().optional(),
   })
   .refine(
      (data) => {
         // If it's a new user, password is required
         if (data.isNewUser) {
            return !!data.password && data.password.length > 0;
         }
         return true;
      },
      {
         message: "Password is required for new users",
         path: ["password"],
      },
   )
   .refine(
      (data) => {
         // If password is provided, confirmPassword must match
         if (data.password && data.password.length > 0) {
            return data.password === data.confirmPassword;
         }
         return true;
      },
      {
         message: "Passwords do not match",
         path: ["confirmPassword"],
      },
   );

export type UserFormType = z.infer<typeof userSchema>;
