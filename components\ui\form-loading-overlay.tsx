"use client";

import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { useSidebar } from "./sidebar";

interface FormLoadingOverlayProps {
   isLoading: boolean;
   message?: string;
   className?: string;
}

/**
 * A loading overlay component that displays a spinner over content
 * Used to indicate loading states in forms
 */
function FormLoadingOverlay({
   isLoading,
   message = "Loading...",
   className,
}: FormLoadingOverlayProps) {
   const { state } = useSidebar();
   if (!isLoading) return null;

   return (
      <div
         className={cn(
            "fixed bottom-0 left-0 right-0 top-0 z-50 flex flex-col items-center justify-center bg-background/80 backdrop-blur-[2px]",
            // Adjust left position based on sidebar state
            state === "expanded"
               ? "md:left-[var(--sidebar-width)]"
               : "md:left-[var(--sidebar-width-icon)]",
            className,
         )}
         aria-live="polite"
         aria-busy={isLoading}
      >
         <div className="flex flex-col items-center justify-center rounded-lg bg-background/90 p-6 shadow-lg">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            {message && (
               <p className="mt-2 text-sm text-muted-foreground">{message}</p>
            )}
         </div>
      </div>
   );
}

export { FormLoadingOverlay };
