import {
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON>ontent,
   <PERSON><PERSON><PERSON>ead<PERSON>,
   <PERSON><PERSON><PERSON><PERSON><PERSON>,
   <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/dialog";
import { useState } from "react";
import EpisodeForm from "./episode-form";
import { Episode } from "@/lib/firebase/types";

type Props = {
   children: React.ReactNode;
   title: string;
   episode?: Episode;
};

function EpisodeDialog({children, title, episode}: Props) {
   const [open, setOpen] = useState(false);

   const closeDialog = () => setOpen(false);

   return (
      <>
         <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
               {children}
            </DialogTrigger>
            <DialogContent>
               <DialogHeader>
                  <DialogTitle>{title}</DialogTitle>
               </DialogHeader>
               <EpisodeForm episode={episode} closeDialog={closeDialog} />
            </DialogContent>
         </Dialog>
      </>
   );
}

export default EpisodeDialog;
