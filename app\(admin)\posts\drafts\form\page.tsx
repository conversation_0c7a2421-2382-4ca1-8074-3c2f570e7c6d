import DraftForm from "@/components/pages/posts/draft-form";
import BackTo from "@/components/ui/back-to";
import Spinner from "@/components/ui/spinner";
import { getAuthors } from "@/lib/firebase/authors/service";
import { getCategories } from "@/lib/firebase/categories/service";
import { Suspense } from "react";

async function DraftFormContent() {
   const categories = await getCategories();
   const authors = await getAuthors();

   return (
      <>
         <DraftForm categories={categories} authors={authors} />
      </>
   );
}

export default function DraftsFormPage() {
   return (
      <>
         <BackTo link="/posts/drafts" text="Back to drafts" />
         <Suspense fallback={<Spinner />}>
            <DraftFormContent />
         </Suspense>
      </>
   );
}
