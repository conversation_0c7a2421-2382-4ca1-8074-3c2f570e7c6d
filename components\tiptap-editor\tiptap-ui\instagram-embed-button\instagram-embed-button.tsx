"use client";

import { type Editor } from "@tiptap/react";
import * as React from "react";

// --- Hooks ---
import { useTiptapEditor } from "@/hooks/use-tiptap-editor";

// --- Icons ---
import { InstagramIcon } from "@/components/tiptap-editor/tiptap-icons/instagram-icon";

// --- UI Components ---
import {
   EmbedButton,
   EmbedButtonProps,
} from "@/components/tiptap-editor/tiptap-ui/embed-button";

export interface InstagramEmbedButtonProps
   extends Omit<EmbedButtonProps, "type" | "icon"> {
   editor?: Editor | null;
   text?: string;
}

export const InstagramEmbedButton = React.forwardRef<
   HTMLButtonElement,
   InstagramEmbedButtonProps
>(
   (
      {
         editor: providedEditor,
         text,
         className = "",
         disabled,
         onClick,
         children,
         ...buttonProps
      },
      ref,
   ) => {
      const editor = useTiptapEditor(providedEditor);

      if (!editor || !editor.isEditable) {
         return null;
      }

      return (
         <EmbedButton
            ref={ref}
            type="instagram"
            icon={<InstagramIcon className="tiptap-button-icon" />}
            text={text}
            editor={editor}
            className={className}
            disabled={disabled}
            onClick={onClick}
            {...buttonProps}
         >
            {children}
         </EmbedButton>
      );
   },
);

InstagramEmbedButton.displayName = "InstagramEmbedButton";

export default InstagramEmbedButton;
