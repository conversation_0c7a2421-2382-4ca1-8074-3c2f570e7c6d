import { z } from "zod";

export const adminSchema = z
   .object({
      name: z
         .string()
         .min(1, { message: "Name is required" })
         .min(3, { message: "Name must be at least 3 characters" }),
      email: z
         .string()
         .min(1, { message: "Email is required" })
         .email({ message: "Please enter a valid email address" }),
      password: z
         .string()
         .min(1, { message: "Password is required" })
         .min(8, { message: "Password must be at least 8 characters" }),
      confirmPassword: z
         .string()
         .min(1, { message: "Confirm Password is required" })
         .min(8, { message: "Password must be at least 8 characters" }),
   })
   .refine((data) => data.password === data.confirmPassword, {
      message: "Passwords do not match",
      path: ["confirmPassword"],
   });

export type AdminFormType = z.infer<typeof adminSchema>;
