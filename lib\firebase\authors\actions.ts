"use server";

import { AuthorFormType } from "@/components/pages/authors/author-form";
import { uploadImage } from "@/lib/cloudinary/cloudinary";
import { slugify } from "@/lib/utils";
import {
   collection,
   deleteDoc,
   doc,
   getDoc,
   getDocs,
   query,
   setDoc,
   updateDoc,
   where,
} from "firebase/firestore";
import { revalidatePath } from "next/cache";
import { db } from "../firebase";

export async function createAuthor(data: AuthorFormType) {
   const { name, image } = data;

   // Generate a slug from the author name
   const slug = slugify(name);

   // Check if a author with the same name already exists
   const authorsRef = collection(db, "authors");
   const q = query(authorsRef, where("name", "==", name));
   const querySnapshot = await getDocs(q);
   if (!querySnapshot.empty) {
      return {
         message: "A author with the same name already exists",
         success: false,
      };
   }

   // Generate a unique ID for the new author document
   const uniqueId = doc(collection(db, "authors")).id;

   let imageUrl = "";

   if (image && image instanceof File) {
      imageUrl = await uploadImage(image, uniqueId);

      if (!imageUrl)
         return {
            message: "Image upload failed, please try again!",
            success: false,
         };
   }

   // Create a new author object
   const newAuthor = {
      ...data,
      name,
      slug,
      ...(imageUrl && { image: imageUrl }),
   };

   const docRef = doc(db, "authors", uniqueId);

   try {
      // Save the new author document to Firestore
      await setDoc(docRef, newAuthor);

      revalidatePath("/authors");

      return {
         message: "Author created successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to create author",
         success: false,
      };
   }
}

export async function updateAuthor(data: AuthorFormType, id: string) {
   const { name, image } = data;

   // Check if a author with the same name already exists
   const movieSnap = await getDoc(doc(db, "authors", id));
   if (!movieSnap.exists()) {
      return {
         message: "Author not found",
         success: false,
      };
   }

   if (!(movieSnap.data().name === name)) {
      const moviesRef = collection(db, "authors");
      const q = query(moviesRef, where("name", "==", name));
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
         return {
            message: "A author with the same name already exists",
            success: false,
         };
      }
   }

   const slug = slugify(name);

   let imageUrl = "";

   if (image) {
      imageUrl =
         typeof image === "string" ? image : await uploadImage(image, id);

      if (!imageUrl)
         return {
            message: "Image upload failed, please try again!",
            success: false,
         };
   }

   const docRef = doc(db, "authors", id);

   try {
      await updateDoc(docRef, {
         ...data,
         name,
         slug,
         ...(imageUrl && { image: imageUrl }),
      });

      revalidatePath("/authors");

      return {
         message: "Author updated successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to update author",
         success: false,
      };
   }
}

export async function deleteAuthor(authorId: string) {
   if (!authorId) {
      return {
         message: "Author ID is required",
         success: false,
      };
   }

   try {
      await deleteDoc(doc(db, "authors", authorId));
      revalidatePath("/authors");

      return {
         message: "Author deleted successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to delete author",
         success: false,
      };
   }
}
