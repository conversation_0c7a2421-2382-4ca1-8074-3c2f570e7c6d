"use client";

import { Checkbox } from "@/components/ui/checkbox";
import { Episode } from "@/lib/firebase/types";
import { ColumnDef, FilterFn } from "@tanstack/react-table";
import { EpisodeAction } from "./episode-actions";

// Custom filter function for multi-column searching
const multiColumnFilterFn: FilterFn<Episode> = (row, columnId, filterValue) => {
   const searchableRowContent = `${row.original.title}`.toLowerCase();
   const searchTerm = (filterValue ?? "").toLowerCase();
   return searchableRowContent.includes(searchTerm);
};

export const columns: ColumnDef<Episode>[] = [
   {
      id: "select",
      header: ({ table }) => (
         <Checkbox
            checked={
               table.getIsAllPageRowsSelected() ||
               (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
               table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
         />
      ),
      cell: ({ row }) => (
         <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
         />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
   },
   {
      header: "No.",
      accessorKey: "episodeNumber",
      cell: ({ row }) => (
         <div className="font-medium">{row.getValue("episodeNumber")}</div>
      ),
      size: 80,
      filterFn: multiColumnFilterFn,
      enableHiding: false,
   },
   {
      header: "Title",
      accessorKey: "title",
      cell: ({ row }) => (
         <div className="font-medium">{row.getValue("title")}</div>
      ),
      size: 200,
      filterFn: multiColumnFilterFn,
      enableHiding: false,
   },
   {
      header: "Description",
      accessorKey: "description",
      cell: ({ row }) => (
         <div className="line-clamp-1">{row.getValue("description")}</div>
      ),
      size: 420,
   },
   {
      id: "actions",
      header: () => <span className="sr-only">Actions</span>,
      cell: ({ row }) => <EpisodeAction row={row} />,
      size: 60,
      enableHiding: false,
   },
];
