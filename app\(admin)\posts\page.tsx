import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { DataTableWrapper } from "@/components/ui/data-table-wrapper";
import { getPosts } from "@/lib/firebase/posts/service";
import { FileTextIcon, PlusIcon } from "lucide-react";
import Link from "next/link";
import { columns } from "./table-columns";

// Separate component for the table content that will be wrapped with Suspense
async function PostsTableContent() {
   const posts = await getPosts();

   return (
      <DataTable
         data={posts}
         columns={columns}
         defaultSortId="title"
         customButton={
            <>
               <Link href="/posts/drafts">
                  <Button variant="outline" className="gap-2">
                     <FileTextIcon size={16} />
                     View Drafts
                  </Button>
               </Link>
               <Link href="/posts/form">
                  <Button className="ml-auto" variant="outline">
                     <PlusIcon
                        className="-ms-1 opacity-60"
                        size={16}
                        aria-hidden="true"
                     />
                     Add post
                  </Button>
               </Link>
            </>
         }
      />
   );
}

async function PostsPage() {
   return (
      <>
         <div className="flex items-center justify-between pb-4">
            <h1 className="text-2xl font-semibold">Published Posts</h1>
         </div>
         <DataTableWrapper columnCount={8}>
            <PostsTableContent />
         </DataTableWrapper>
      </>
   );
}

export default PostsPage;
