import { DataTable } from "@/components/ui/data-table";
import { DataTableWrapper } from "@/components/ui/data-table-wrapper";
import { PlusIcon } from "lucide-react";
import { useEpisodes } from "./context/episodesProvider";
import EpisodeDialog from "./episode-dialog";
import { columns } from "./episodes-column";

// Separate component for the table content
function EpisodesTableContent() {
   const { episodes, handleDeleteEpisodes } = useEpisodes();

   return (
      <DataTable
         data={episodes}
         columns={columns}
         defaultSortId="episodeNumber"
         showPagination={false}
         toggleColumnVisibility={false}
         deleteAction={handleDeleteEpisodes}
         customButton={
            <EpisodeDialog title="Add Episode">
               <span className="ml-auto inline-flex h-9 cursor-pointer items-center justify-center gap-2 whitespace-nowrap rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0">
                  <PlusIcon
                     className="-ms-1 opacity-60"
                     size={16}
                     aria-hidden="true"
                  />
                  Add episode
               </span>
            </EpisodeDialog>
         }
      />
   );
}

function EpisodesTable() {
   return (
      <DataTableWrapper
         columnCount={5}
         hasCheckbox={true}
         hasAvatar={false}
         showFilters={false}
      >
         <EpisodesTableContent />
      </DataTableWrapper>
   );
}

export default EpisodesTable;
