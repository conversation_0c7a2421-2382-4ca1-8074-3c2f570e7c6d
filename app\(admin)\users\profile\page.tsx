import { auth } from "@/auth";
import UserProfileForm from "@/components/pages/users/user-profile-form";
import BackTo from "@/components/ui/back-to";
import { redirect } from "next/navigation";

async function UserProfilePage() {
   // Server-side check for user role
   const session = await auth();

   // If the user is an editor, redirect to dashboard
   if (session?.user?.role === "editor") {
      redirect("/dashboard");
   }

   return (
      <>
         <BackTo link="/users" text="Back to users" />
         <UserProfileForm />
      </>
   );
}

export default UserProfilePage;
