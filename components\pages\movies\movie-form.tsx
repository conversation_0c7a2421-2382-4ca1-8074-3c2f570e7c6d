"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { Input } from "@/components/ui/input";
import MultipleSelector from "@/components/ui/multiselect";
import { ACCEPTED_IMAGE_TYPES, MAX_FILE_SIZE } from "@/lib/constants";
import { createMovie, updateMovie } from "@/lib/firebase/movies/actions";
import { getMovieById } from "@/lib/firebase/movies/service";
import { formatBytes } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useEffect, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { genres } from "./genres";

const formSchema = z.object({
   title: z.string().min(3, {
      message: "Title must be at least 3 characters.",
   }),
   year: z
      .string()
      .refine(
         (year) =>
            Number(year) >= 1900 && Number(year) <= new Date().getFullYear(),
         {
            message: `Year must be a 4 digit number between 1900 and ${new Date().getFullYear()}.`,
         },
      ),
   description: z.string().min(10, {
      message: "Description must be at least 10 characters.",
   }),
   poster: z.union([
      z
         .instanceof(File, {
            message: "Please select an image file.",
         })
         .refine((file) => file.size <= MAX_FILE_SIZE, {
            message: `The image is too large. Please choose an image smaller than ${formatBytes(MAX_FILE_SIZE)}.`,
         })
         .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
            message: "Please upload a valid image file (JPEG, PNG, or WebP).",
         }),
      z.string().url({
         message: "Please provide a valid URL for the image.",
      }),
   ]),
   trailerLink: z.string().url({
      message: "Please provide a valid URL for the trailer.",
   }),
   movieLink: z.string().url({
      message: "Please provide a valid URL for the movie.",
   }),
   duration: z.number().min(1, {
      message: "Duration must be a positive number.",
   }),
   genres: z
      .array(
         z.object({
            value: z.string(),
            label: z.string(),
         }),
      )
      .min(1, {
         message: "Please select at least one genre.",
      }),
});

export type MovieFormType = z.infer<typeof formSchema>;

function MovieForm() {
   const searchParams = useSearchParams();
   const id = searchParams.get("id");

   const [pending, startTransition] = useTransition();
   const [error, setError] = useState({ message: "" });
   const [image, setImage] = useState<string | Blob>("");
   const [dataLoaded, setDataLoaded] = useState<boolean | undefined>(
      id ? false : undefined,
   );

   const form = useForm<z.infer<typeof formSchema>>({
      resolver: zodResolver(formSchema),
      defaultValues: {
         title: "",
         year: "",
         description: "",
         trailerLink: "",
         movieLink: "",
         genres: [],
         duration: 0,
         poster: undefined,
      },
   });

   async function onSubmit(values: z.infer<typeof formSchema>) {
      startTransition(async () => {
         const result = id
            ? await updateMovie(values, id)
            : await createMovie(values);

         if (result?.message) {
            return setError(result);
         }

         form.reset();
      });
   }

   useEffect(() => {
      async function getMovieData() {
         if (!id) return;

         const data = await getMovieById(id);

         if (data) {
            form.setValue("title", data.title);
            form.setValue("year", data.year.toString());
            form.setValue("description", data.description);
            form.setValue("trailerLink", data.trailerLink);
            form.setValue("movieLink", data.movieLink);
            form.setValue(
               "genres",
               data.genres.map((genre) => ({ value: genre, label: genre })),
            );
            form.setValue("duration", data.duration);
            form.setValue("poster", data.poster);
            setImage(data.poster);
            setDataLoaded(true);
         } else {
            setDataLoaded(false);
         }
      }

      getMovieData();
   }, [id, form]);

   return (
      <FormWrapper isSubmitting={pending} isDataLoaded={dataLoaded}>
         <h1 className="pb-4 text-2xl font-semibold">
            {id ? "Update" : "Add New"} Movie
         </h1>

         <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
               <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Movie Title</FormLabel>
                        <FormControl>
                           <Input placeholder="Movie Title" {...field} />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name="year"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Release Year</FormLabel>
                        <FormControl>
                           <Input
                              type="number"
                              placeholder="Release Year"
                              {...field}
                           />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Movie Description</FormLabel>
                        <FormControl>
                           <Input placeholder="Movie Description" {...field} />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name="trailerLink"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Trailer Link</FormLabel>
                        <FormControl>
                           <Input placeholder="Trailer Link" {...field} />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name="movieLink"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Movie Link</FormLabel>
                        <FormControl>
                           <Input placeholder="Movie Link" {...field} />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name="genres"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Genres</FormLabel>
                        <FormControl>
                           <MultipleSelector
                              {...field}
                              defaultOptions={genres}
                              placeholder="Select genres"
                              hidePlaceholderWhenSelected
                              emptyIndicator={
                                 <p className="text-center text-sm">
                                    No results found
                                 </p>
                              }
                           />
                        </FormControl>
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name="duration"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Duration (minutes)</FormLabel>
                        <FormControl>
                           <Input
                              {...field}
                              type="number"
                              placeholder="Duration (minutes)"
                              onChange={(e) =>
                                 field.onChange(Number(e.target.value))
                              }
                           />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name="poster"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Movie Image</FormLabel>
                        <FormControl>
                           <Input
                              type="file"
                              accept="image/*"
                              placeholder="Movie Image"
                              onChange={(e) => {
                                 const file = e.target.files?.[0];
                                 if (file) {
                                    field.onChange(file);
                                    setImage(file);
                                 }
                              }}
                           />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               {image && (
                  <div className="relative aspect-square h-20 overflow-hidden rounded-xl">
                     <Image
                        className="object-cover"
                        src={
                           typeof image === "string"
                              ? image
                              : URL.createObjectURL(image as Blob)
                        }
                        alt={""}
                        fill
                     />
                  </div>
               )}

               {error.message && (
                  <p className="text-sm font-medium text-destructive">
                     {error.message}
                  </p>
               )}
               <Button type="submit" disabled={pending}>
                  {pending && <Loader2 className="animate-spin" />}
                  {pending ? "" : id ? "Update" : "Add"}
               </Button>
            </form>
         </Form>
      </FormWrapper>
   );
}

export default MovieForm;
