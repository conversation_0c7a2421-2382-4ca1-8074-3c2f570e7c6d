"use client";

import { useCallback, useEffect, useState } from "react";

type PersistentStateOptions<T> = {
   /**
    * The key to use for storing data in localStorage
    */
   key: string;

   /**
    * Default value to use if no value is found in localStorage
    */
   defaultValue: T;
};

/**
 * Hook for persisting state in localStorage
 * @param options Configuration options for the persistent state
 * @returns [state, setState, removeState] - The current state, a function to update the state, and a function to remove the state
 */
export function usePersistentState<T>(
   options: PersistentStateOptions<T>,
): [T, (value: T | ((prevValue: T) => T)) => void, () => void] {
   const { key, defaultValue } = options;

   // Use useState with the default value initially to avoid hydration mismatches
   const [state, setState] = useState<T>(defaultValue);

   // Use a ref to track if we've hydrated
   const [isHydrated, setIsHydrated] = useState(false);

   // After hydration, load the state from localStorage
   useEffect(() => {
      setIsHydrated(true);

      try {
         const item = window.localStorage.getItem(key);
         if (item) {
            const parsedValue = JSON.parse(item);
            setState(parsedValue);
         }
      } catch (error) {
         console.error(
            "Error loading persistent state from localStorage:",
            error,
         );
      }
   }, [key]);

   // Update localStorage when state changes, but only after hydration
   useEffect(() => {
      if (!isHydrated) {
         return;
      }

      try {
         window.localStorage.setItem(key, JSON.stringify(state));
      } catch (error) {
         console.error("Error saving persistent state to localStorage:", error);
      }
   }, [key, state, isHydrated]);

   // Function to remove the state from localStorage
   const removeState = useCallback(() => {
      if (!isHydrated) {
         return;
      }

      try {
         window.localStorage.removeItem(key);
         setState(defaultValue);
      } catch (error) {
         console.error(
            "Error removing persistent state from localStorage:",
            error,
         );
      }
   }, [key, defaultValue, isHydrated]);

   return [state, setState, removeState];
}
