"use server";

import { SeriesFormType } from "@/components/pages/series/series-form";
import { uploadImage } from "@/lib/cloudinary/cloudinary";
import { slugify } from "@/lib/utils";
import {
   collection,
   deleteDoc,
   doc,
   getDoc,
   getDocs,
   query,
   setDoc,
   Timestamp,
   updateDoc,
   where,
} from "firebase/firestore";
import { revalidatePath } from "next/cache";
import { db } from "../firebase";
import { Episode } from "../types";

export async function createSeries(data: SeriesFormType, episodes: Episode[]) {
   const { title, poster, genres } = data;

   // Generate a slug from the series title
   const slug = slugify(title);

   // Check if a series with the same title already exists
   const seriesRef = collection(db, "series");
   const q = query(seriesRef, where("title", "==", title));
   const querySnapshot = await getDocs(q);
   if (!querySnapshot.empty) {
      return {
         message: "A series with the same title already exists",
         success: false,
      };
   }

   // Generate a unique ID for the new series document
   const uniqueId = doc(collection(db, "series")).id;

   // Upload image and get the URL
   const imageUrl =
      poster instanceof File && (await uploadImage(poster, uniqueId));
   if (!imageUrl) {
      return {
         message: "Image upload failed, please try again!",
         success: false,
      };
   }

   // Extract genre values
   const genreValues = genres.map((genre) => genre.value);

   // Create a new series object
   const newSeries = {
      ...data,
      slug,
      poster: imageUrl,
      genres: genreValues,
      category: "series",
      episodes,
      createdAt: Timestamp.now(),
   };

   const docRef = doc(db, "series", uniqueId);

   try {
      // Save the new series document to Firestore
      await setDoc(docRef, newSeries);

      revalidatePath("/series");

      return {
         message: "Series created successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to create series",
         success: false,
      };
   }
}

export async function updateSeries(
   data: SeriesFormType,
   id: string,
   episodes: Episode[],
) {
   const { title, poster, genres } = data;

   // Generate a slug from the series title
   const slug = slugify(title);

   // Check if a series with the same title already exists
   const seriesSnap = await getDoc(doc(db, "series", id));
   if (!seriesSnap.exists()) {
      return {
         message: "Series not found",
         success: false,
      };
   }

   if (!(seriesSnap.data().title === title)) {
      const seriesRef = collection(db, "series");
      const q = query(seriesRef, where("title", "==", title));
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
         return {
            message: "A series with the same title already exists",
            success: false,
         };
      }
   }

   // Upload image and get the URL
   const imageUrl =
      typeof poster === "string" ? poster : await uploadImage(poster, id);
   if (!imageUrl) {
      return {
         message: "Image upload failed, please try again!",
         success: false,
      };
   }

   // Extract genre values
   const genreValues = genres.map((genre) => genre.value);

   const docRef = doc(db, "series", id);

   try {
      await updateDoc(docRef, {
         ...data,
         title,
         slug,
         poster: imageUrl,
         genres: genreValues,
         episodes,
      });

      revalidatePath("/series");

      return {
         message: "Series updated successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to update series",
         success: false,
      };
   }
}

export async function deleteSeries(seriesId: string) {
   if (!seriesId) {
      return {
         message: "Series ID is required",
         success: false,
      };
   }

   try {
      await deleteDoc(doc(db, "series", seriesId));
      revalidatePath("/series");

      return {
         message: "Series deleted successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to delete series",
         success: false,
      };
   }
}
