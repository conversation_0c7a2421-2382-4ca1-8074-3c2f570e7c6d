"use server";

import { getAuthorById } from "@/lib/firebase/authors/service";
import { getCategoryById } from "@/lib/firebase/categories/service";
import { db } from "@/lib/firebase/firebase";
import { Draft } from "@/lib/firebase/types";
import {
   collection,
   doc,
   getDoc,
   getDocs,
   orderBy,
   query,
   where,
} from "firebase/firestore";

export async function getDrafts() {
   const draftsRef = collection(db, "drafts");

   const q = query(draftsRef, orderBy("createdAt", "desc"));

   const querySnapshot = await getDocs(q);

   const drafts = await Promise.all(
      querySnapshot.docs.map(async (doc) => {
         const { authorId, categoryId, createdAt, updatedAt, ...rest } =
            doc.data();

         return {
            id: doc.id,
            ...rest,
            author: authorId ? await getAuthorById(authorId) : undefined,
            category: categoryId
               ? await getCategoryById(categoryId)
               : undefined,
            createdAt: createdAt.toDate(),
            updatedAt: updatedAt.toDate(),
         } as Draft;
      }),
   );

   return drafts;
}

export async function getDraftById(id: string) {
   const docRef = doc(db, "drafts", id);

   const docSnap = await getDoc(docRef);

   if (!docSnap.exists()) {
      throw new Error(`Draft with id ${id} not found`);
   }

   const { authorId, categoryId, createdAt, updatedAt, ...rest } =
      docSnap.data();

   const draft = {
      id: docSnap.id,
      ...rest,
      author: authorId ? await getAuthorById(authorId) : undefined,
      category: categoryId ? await getCategoryById(categoryId) : undefined,
      createdAt: createdAt.toDate(),
      updatedAt: updatedAt.toDate(),
   } as Draft;

   return draft;
}

export async function getDraftBySlug(slug: string) {
   const draftsRef = collection(db, "drafts");

   const q = query(draftsRef, where("slug", "==", slug));

   const querySnapshot = await getDocs(q);

   if (querySnapshot.empty) return null;

   const draftDoc = querySnapshot.docs[0];

   const { authorId, categoryId, createdAt, updatedAt, ...rest } =
      draftDoc.data();

   const draft = {
      id: draftDoc.id,
      ...rest,
      author: authorId ? await getAuthorById(authorId) : undefined,
      category: categoryId ? await getCategoryById(categoryId) : undefined,
      createdAt: createdAt.toDate(),
      updatedAt: updatedAt.toDate(),
   } as Draft;

   return draft;
}
