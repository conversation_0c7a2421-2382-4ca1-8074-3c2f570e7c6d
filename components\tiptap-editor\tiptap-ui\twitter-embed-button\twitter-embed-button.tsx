"use client";

import { type Editor } from "@tiptap/react";
import * as React from "react";

// --- Hooks ---
import { useTiptapEditor } from "@/hooks/use-tiptap-editor";

// --- Icons ---
import { TwitterIcon } from "@/components/tiptap-editor/tiptap-icons/twitter-icon";

// --- UI Components ---
import {
   EmbedButton,
   EmbedButtonProps,
} from "@/components/tiptap-editor/tiptap-ui/embed-button";

export interface TwitterEmbedButtonProps
   extends Omit<EmbedButtonProps, "type" | "icon"> {
   editor?: Editor | null;
   text?: string;
}

export const TwitterEmbedButton = React.forwardRef<
   HTMLButtonElement,
   TwitterEmbedButtonProps
>(
   (
      {
         editor: providedEditor,
         text,
         className = "",
         disabled,
         onClick,
         children,
         ...buttonProps
      },
      ref,
   ) => {
      const editor = useTiptapEditor(providedEditor);

      if (!editor || !editor.isEditable) {
         return null;
      }

      return (
         <EmbedButton
            ref={ref}
            type="twitter"
            icon={<TwitterIcon className="tiptap-button-icon" />}
            text={text}
            editor={editor}
            className={className}
            disabled={disabled}
            onClick={onClick}
            {...buttonProps}
         >
            {children}
         </EmbedButton>
      );
   },
);

TwitterEmbedButton.displayName = "TwitterEmbedButton";

export default TwitterEmbedButton;
