"use server";

import { PostFormType } from "@/components/pages/posts/post-form";
import { uploadImage } from "@/lib/cloudinary/cloudinary";
import { slugify } from "@/lib/utils";
import {
   addDoc,
   collection,
   deleteDoc,
   doc,
   getDoc,
   getDocs,
   query,
   Timestamp,
   updateDoc,
   where,
} from "firebase/firestore";
import { revalidatePath } from "next/cache";
import { db } from "../firebase";

const tags = [
   ["Marvel", "Cinematic Universe", "Most"],
   ["NFL", "Football", "Sports"],
   ["Kanye West", "Rapper", "Celebrity"],
   ["Lord of the Rings", "Book", "Fantasy"],
   ["Avengers", "Superhero", "Marvel"],
   ["Premier League", "Football", "Sports"],
];

export async function createPost(data: PostFormType) {
   const { title, poster, sections } = data;

   // Generate a slug from the post title
   const slug = slugify(title);

   // Check if a post with the same title already exists
   const postsRef = collection(db, "posts");
   const q = query(postsRef, where("title", "==", title));
   const querySnapshot = await getDocs(q);
   if (!querySnapshot.empty) {
      return {
         message: "A post with the same title already exists",
         success: false,
      };
   }

   // Generate a unique ID for the new post document
   const uniqueId = doc(collection(db, "posts")).id;

   // Upload image and get the URL
   let imageUrl;
   if (poster instanceof File) {
      imageUrl = await uploadImage(poster, uniqueId);
      if (!imageUrl)
         return {
            message: "Image upload failed, please try again!",
            success: false,
         };
   } else if (typeof poster === "string") {
      imageUrl = poster;
   } else {
      return {
         message: "Invalid poster format",
         success: false,
      };
   }

   const newPost = {
      ...data,
      title,
      slug,
      ...(imageUrl && { poster: imageUrl }),
      tags: data.tags || tags[Math.floor(Math.random() * tags.length)],
      sections: sections || [],
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
   };

   try {
      const docRef = await addDoc(collection(db, "posts"), newPost);

      if (!docRef) {
         return {
            message: "Failed to create post",
            success: false,
         };
      }

      revalidatePath("/posts");

      return {
         message: "Post created successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to create post",
         success: false,
      };
   }
}

export async function updatePost(data: PostFormType, id: string) {
   const { title, poster, sections } = data;

   // Generate a slug from the post title
   const slug = slugify(title);

   // Check if a post with the same title already exists
   const postSnap = await getDoc(doc(db, "posts", id));
   if (!postSnap.exists()) {
      return {
         message: "Post not found",
         success: false,
      };
   }

   if (!(postSnap.data().title === title)) {
      const postsRef = collection(db, "posts");
      const q = query(postsRef, where("title", "==", title));
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
         return {
            message: "A post with the same title already exists",
            success: false,
         };
      }
   }

   // Upload image and get the URL
   const imageUrl =
      typeof poster === "string" ? poster : await uploadImage(poster, id);
   if (!imageUrl) {
      return {
         message: "Image upload failed, please try again!",
         success: false,
      };
   }

   const docRef = doc(db, "posts", id);

   try {
      await updateDoc(docRef, {
         ...data,
         title,
         slug,
         ...(imageUrl && { poster: imageUrl }),
         sections: sections || [],
         updatedAt: Timestamp.now(),
      });

      revalidatePath("/posts");

      return {
         message: "Post updated successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to update post",
         success: false,
      };
   }
}

export async function uploadPostImage(file: File) {
   try {
      const imageUrl = await uploadImage(
         file,
         `post-${file.name}-${Date.now()}`,
      );

      if (!imageUrl) {
         return "https://placehold.co/600x400/EEE/31343C/png?text=Error+uploading+image&font=poppins";
      }

      return imageUrl;
   } catch (error) {
      console.log(error);
      return "https://placehold.co/600x400/EEE/31343C/png?text=Error+uploading+image&font=poppins";
   }
}

export async function deletePost(postId: string) {
   if (!postId) {
      return {
         message: "Post ID is required",
         success: false,
      };
   }

   try {
      await deleteDoc(doc(db, "posts", postId));
      revalidatePath("/posts");

      return {
         message: "Post deleted successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to delete post",
         success: false,
      };
   }
}
