"use client";

import { SimpleEditor } from "@/components/tiptap-editor/simple-editor";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
   Form,
   FormControl,
   FormDescription,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { Input } from "@/components/ui/input";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import { ACCEPTED_IMAGE_TYPES, MAX_FILE_SIZE } from "@/lib/constants";
import { POST_SECTIONS } from "@/lib/constants/post-sections";
import {
   DraftFormType,
   createDraft,
   publishDraft,
   updateDraft,
} from "@/lib/firebase/drafts/actions";
import { getDraftById } from "@/lib/firebase/drafts/service";
import { Author, Category } from "@/lib/firebase/types";
import { formatBytes } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import {
   BookOpenText,
   ImageIcon,
   Layers,
   Loader2,
   PenLine,
   Tags,
} from "lucide-react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

// Draft form schema with only title required
const formSchema = z.object({
   title: z.string().min(3, {
      message: "Title must be at least 3 characters.",
   }),
   summary: z.string().optional(),
   categoryId: z.string().optional(),
   authorId: z.string().optional(),
   poster: z
      .union([
         z
            .instanceof(File, {
               message: "Please select an image file.",
            })
            .refine((file) => file.size <= MAX_FILE_SIZE, {
               message: `The image is too large. Please choose an image smaller than ${formatBytes(MAX_FILE_SIZE)}.`,
            })
            .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
               message:
                  "Please upload a valid image file (JPEG, PNG, or WebP).",
            }),
         z.string().url({
            message: "Please provide a valid URL for the image.",
         }),
      ])
      .optional(),
   tags: z.array(z.string()).default([]),
   content: z.string().optional(),
   sections: z.array(z.string()).default([]),
});

// Full post schema for validation before publishing
const publishSchema = z.object({
   title: z.string().min(3, {
      message: "Title must be at least 3 characters.",
   }),
   summary: z.string().min(5, {
      message: "Summary must be at least 5 characters.",
   }),
   categoryId: z.string({
      required_error: "Category is required.",
   }),
   authorId: z.string({
      required_error: "Author is required.",
   }),
   poster: z.union([
      z
         .instanceof(File, {
            message: "Please select an image file.",
         })
         .refine((file) => file.size <= MAX_FILE_SIZE, {
            message: `The image is too large. Please choose an image smaller than ${formatBytes(MAX_FILE_SIZE)}.`,
         })
         .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
            message: "Please upload a valid image file (JPEG, PNG, or WebP).",
         }),
      z.string().url({
         message: "Please provide a valid URL for the image.",
      }),
   ]),
   tags: z.array(z.string()),
   content: z.string().min(10, {
      message: "Content must be at least 10 characters.",
   }),
   sections: z.array(z.string()).default([]),
});

type Props = {
   categories: Category[];
   authors: Author[];
};

function DraftForm({ categories, authors }: Props) {
   const searchParams = useSearchParams();
   const router = useRouter();
   const id = searchParams.get("id");

   const [pending, startTransition] = useTransition();
   const [error, setError] = useState({ message: "" });
   const [image, setImage] = useState<string | Blob>("");
   const [initialContent, setInitialContent] = useState("");
   const [dataLoaded, setDataLoaded] = useState<boolean | undefined>(
      id ? false : undefined,
   );
   const [publishErrors, setPublishErrors] = useState<Record<string, string>>(
      {},
   );

   // Create a reference to the file input
   const fileInputRef = useRef<HTMLInputElement>(null);

   // Function to trigger file input click
   const handleImageContainerClick = () => {
      if (fileInputRef.current) {
         fileInputRef.current.click();
      }
   };

   const form = useForm<z.infer<typeof formSchema>>({
      resolver: zodResolver(formSchema),
      defaultValues: {
         title: "",
         summary: "",
         poster: undefined,
         tags: [],
         content: "",
         sections: [],
      },
   });

   async function onSubmit(values: z.infer<typeof formSchema>) {
      startTransition(async () => {
         const result = id
            ? await updateDraft(values as DraftFormType, id)
            : await createDraft(values as DraftFormType);

         if (!result.success) {
            setError(result);
            toast.error(result.message);
            return;
         }

         toast.success(result.message);
         form.reset();
         router.push("/posts/drafts");
      });
   }

   async function onPublish() {
      // Validate all fields using the publish schema
      try {
         const values = form.getValues();
         const result = publishSchema.safeParse(values);

         if (!result.success) {
            const formattedErrors: Record<string, string> = {};
            result.error.errors.forEach((error) => {
               const path = error.path.join(".");
               formattedErrors[path] = error.message;
            });
            setPublishErrors(formattedErrors);
            return;
         }

         // Clear any previous errors
         setPublishErrors({});

         // If validation passes, publish the draft
         startTransition(async () => {
            try {
               if (id) {
                  const result = await publishDraft(id);
                  if (!result.success) {
                     setError(result);
                     toast.error(result.message);
                     return;
                  }

                  toast.success(result.message);
                  router.push("/posts");
               } else {
                  // For new drafts, save first then publish
                  const values = form.getValues();
                  const saveResult = await createDraft(values as DraftFormType);
                  if (!saveResult.success) {
                     setError(saveResult);
                     toast.error(saveResult.message);
                     return;
                  }

                  // Now publish the newly created draft
                  const newDraftId = saveResult.draftId;
                  if (!newDraftId) {
                     setError({ message: "Failed to get draft ID" });
                     toast.error("Failed to get draft ID");
                     return;
                  }

                  const publishResult = await publishDraft(newDraftId);
                  if (!publishResult.success) {
                     setError(publishResult);
                     toast.error(publishResult.message);
                     return;
                  }

                  toast.success(publishResult.message);
                  router.push("/posts");
               }
            } catch (error) {
               console.error("Publishing error:", error);
               setError({
                  message:
                     error instanceof Error
                        ? error.message
                        : "Failed to publish draft",
               });
            }
         });
      } catch (error) {
         console.error("Publish validation error:", error);
         setError({
            message:
               "Failed to publish draft. Please check all required fields.",
         });
      }
   }

   useEffect(() => {
      async function getDraftData() {
         if (!id) return;
         console.log("Getting draft data...");

         try {
            const data = await getDraftById(id);

            if (data) {
               form.setValue("title", data.title);
               if (data.summary) form.setValue("summary", data.summary);
               if (data.category)
                  form.setValue("categoryId", data.category?.id);
               if (data.author) form.setValue("authorId", data.author?.id);
               if (data.poster) {
                  form.setValue("poster", data.poster);
                  setImage(data.poster);
               }
               if (data.tags) form.setValue("tags", data.tags);
               if (data.sections) form.setValue("sections", data.sections);
               if (data.content) {
                  console.log(
                     "Setting initialContent in draft-form:",
                     data.content.substring(0, 100) + "...",
                  );
                  setInitialContent(data.content);
                  form.setValue("content", data.content);
               }
               setDataLoaded(true);
               console.log("Draft data loaded successfully");
            } else {
               setDataLoaded(false);
            }
         } catch (error) {
            console.error("Error loading draft:", error);
            toast.error("Draft not found or invalid ID");
            router.push("/posts/drafts");
         }
      }

      getDraftData();
   }, [id, form, router]);

   return (
      <FormWrapper isSubmitting={pending} isDataLoaded={dataLoaded}>
         <div className="container mx-auto max-w-5xl">
            <div className="mb-4 text-center">
               <h1 className="text-2xl font-bold tracking-tight">
                  {id ? "Edit Draft" : "Create a New Draft"}
               </h1>
            </div>

            <Form {...form}>
               <form className="space-y-6">
                  {/* Main content section */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <PenLine className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">Draft Details</h2>
                     </div>
                     <div className="space-y-6">
                        <FormField
                           control={form.control}
                           name="title"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="text-base font-medium">
                                    Draft Title
                                 </FormLabel>
                                 <FormDescription>
                                    Create a title for your draft (required)
                                 </FormDescription>
                                 <FormControl>
                                    <Input
                                       placeholder="Enter draft title here"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                                 {publishErrors.title && (
                                    <p className="text-sm font-medium text-destructive">
                                       {publishErrors.title}
                                    </p>
                                 )}
                              </FormItem>
                           )}
                        />

                        <FormField
                           control={form.control}
                           name="summary"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="text-base font-medium">
                                    Post Summary
                                 </FormLabel>
                                 <FormDescription>
                                    Write a brief summary that will appear in
                                    previews (optional for draft)
                                 </FormDescription>
                                 <FormControl>
                                    <Input
                                       placeholder="Summarize post in a few sentences"
                                       {...field}
                                       value={field.value || ""}
                                    />
                                 </FormControl>
                                 <FormMessage />
                                 {publishErrors.summary && (
                                    <p className="text-sm font-medium text-destructive">
                                       {publishErrors.summary}
                                    </p>
                                 )}
                              </FormItem>
                           )}
                        />
                     </div>
                  </div>

                  {/* Featured Image Section */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <ImageIcon className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">
                           Featured Image (Optional for Draft)
                        </h2>
                     </div>

                     <FormField
                        control={form.control}
                        name="poster"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel className="text-base font-medium">
                                 Cover Image
                              </FormLabel>
                              <FormDescription>
                                 Upload an image for your post (recommended
                                 size: 1200×630px)
                              </FormDescription>
                              <div className="mt-2">
                                 {image ? (
                                    <div
                                       className="relative mb-4 cursor-pointer overflow-hidden rounded-lg border-2 border-dashed border-border bg-muted/20 transition-colors hover:bg-muted/30"
                                       onClick={handleImageContainerClick}
                                    >
                                       <div className="relative aspect-video w-full overflow-hidden rounded-md">
                                          <Image
                                             className="object-cover"
                                             src={
                                                typeof image === "string"
                                                   ? image
                                                   : URL.createObjectURL(
                                                        image as Blob,
                                                     )
                                             }
                                             alt="Featured post image"
                                             fill
                                          />
                                       </div>
                                       <div className="p-3 text-center text-sm text-muted-foreground">
                                          Click to change the image
                                       </div>
                                    </div>
                                 ) : (
                                    <div
                                       className="flex aspect-video cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed border-border bg-muted/20 p-12 text-center transition-colors hover:bg-muted/30"
                                       onClick={handleImageContainerClick}
                                    >
                                       <ImageIcon className="mb-4 h-12 w-12 text-muted-foreground" />
                                       <p className="mb-2 text-sm font-medium text-muted-foreground">
                                          Click to browse for an image
                                       </p>
                                       <p className="text-xs text-muted-foreground">
                                          Supports JPEG, PNG, and WebP (max{" "}
                                          {formatBytes(MAX_FILE_SIZE)})
                                       </p>
                                    </div>
                                 )}
                              </div>
                              <FormControl>
                                 <Input
                                    ref={fileInputRef}
                                    type="file"
                                    accept="image/*"
                                    className="mt-2 hidden"
                                    onChange={(e) => {
                                       const file = e.target.files?.[0];
                                       if (file) {
                                          field.onChange(file);
                                          setImage(file);
                                       }
                                    }}
                                 />
                              </FormControl>
                              <FormMessage />
                              {publishErrors.poster && (
                                 <p className="text-sm font-medium text-destructive">
                                    {publishErrors.poster}
                                 </p>
                              )}
                           </FormItem>
                        )}
                     />
                  </div>

                  {/* Content Editor Section */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <BookOpenText className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">
                           Draft Content (Optional)
                        </h2>
                     </div>

                     <FormField
                        control={form.control}
                        name="content"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel className="text-base font-medium">
                                 Write Your Story
                              </FormLabel>
                              <FormDescription>
                                 Use the editor below to craft your post with
                                 rich formatting
                              </FormDescription>
                              <div className="relative mt-2 rounded-lg border border-border/50">
                                 <FormControl>
                                    <SimpleEditor
                                       key={`editor-${id || "new"}`}
                                       onChange={(value) => {
                                          field.onChange(value);
                                       }}
                                       initialContent={initialContent}
                                    />
                                 </FormControl>
                              </div>
                              <FormMessage />
                              {publishErrors.content && (
                                 <p className="text-sm font-medium text-destructive">
                                    {publishErrors.content}
                                 </p>
                              )}
                           </FormItem>
                        )}
                     />
                  </div>

                  {/* Publishing Details Section */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <Tags className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">
                           Publishing Details (Optional for Draft)
                        </h2>
                     </div>

                     <div className="grid gap-6 md:grid-cols-2">
                        <FormField
                           control={form.control}
                           name="categoryId"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="text-base font-medium">
                                    Category
                                 </FormLabel>
                                 <FormDescription>
                                    Choose the most relevant category for your
                                    post
                                 </FormDescription>
                                 <FormControl>
                                    <Select
                                       {...field}
                                       value={field.value || ""}
                                       onValueChange={(value) =>
                                          field.onChange(value)
                                       }
                                    >
                                       <SelectTrigger className="w-full">
                                          <SelectValue placeholder="Select a category" />
                                       </SelectTrigger>
                                       <SelectContent>
                                          {categories.map((category) => (
                                             <SelectItem
                                                key={category.name}
                                                value={category.id}
                                             >
                                                {category.name}
                                             </SelectItem>
                                          ))}
                                       </SelectContent>
                                    </Select>
                                 </FormControl>
                                 <FormMessage />
                                 {publishErrors.categoryId && (
                                    <p className="text-sm font-medium text-destructive">
                                       {publishErrors.categoryId}
                                    </p>
                                 )}
                              </FormItem>
                           )}
                        />

                        <FormField
                           control={form.control}
                           name="authorId"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel className="text-base font-medium">
                                    Author
                                 </FormLabel>
                                 <FormDescription>
                                    Select the author of this post
                                 </FormDescription>
                                 <FormControl>
                                    <Select
                                       {...field}
                                       value={field.value || ""}
                                       onValueChange={(value) =>
                                          field.onChange(value)
                                       }
                                    >
                                       <SelectTrigger className="w-full">
                                          <SelectValue placeholder="Select an author" />
                                       </SelectTrigger>
                                       <SelectContent>
                                          {authors.map((author) => (
                                             <SelectItem
                                                key={author.name}
                                                value={author.id}
                                             >
                                                {author.name}
                                             </SelectItem>
                                          ))}
                                       </SelectContent>
                                    </Select>
                                 </FormControl>
                                 <FormMessage />
                                 {publishErrors.authorId && (
                                    <p className="text-sm font-medium text-destructive">
                                       {publishErrors.authorId}
                                    </p>
                                 )}
                              </FormItem>
                           )}
                        />
                     </div>
                  </div>

                  {/* Post Sections */}
                  <div className="rounded-lg border border-border/50 bg-card p-6 shadow-sm">
                     <div className="mb-6 flex items-center gap-2">
                        <Layers className="h-4 w-4 text-primary" />
                        <h2 className="text-lg font-semibold">
                           Post Sections (Optional)
                        </h2>
                     </div>

                     <FormField
                        control={form.control}
                        name="sections"
                        render={() => (
                           <FormItem>
                              <div className="mb-4">
                                 <FormLabel className="text-base font-medium">
                                    Display Sections
                                 </FormLabel>
                                 <FormDescription>
                                    Select which sections this post should
                                    appear in
                                 </FormDescription>
                              </div>
                              <div className="grid gap-4 md:grid-cols-3">
                                 {POST_SECTIONS.map((section) => (
                                    <FormField
                                       key={section.id}
                                       control={form.control}
                                       name="sections"
                                       render={({ field }) => {
                                          return (
                                             <FormItem
                                                key={section.id}
                                                className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4"
                                             >
                                                <FormControl>
                                                   <Checkbox
                                                      checked={field.value?.includes(
                                                         section.id,
                                                      )}
                                                      onCheckedChange={(
                                                         checked,
                                                      ) => {
                                                         return checked
                                                            ? field.onChange([
                                                                 ...field.value,
                                                                 section.id,
                                                              ])
                                                            : field.onChange(
                                                                 field.value?.filter(
                                                                    (value) =>
                                                                       value !==
                                                                       section.id,
                                                                 ),
                                                              );
                                                      }}
                                                   />
                                                </FormControl>
                                                <div className="space-y-1 leading-none">
                                                   <FormLabel>
                                                      {section.name}
                                                   </FormLabel>
                                                   <FormDescription>
                                                      {section.description}
                                                   </FormDescription>
                                                </div>
                                             </FormItem>
                                          );
                                       }}
                                    />
                                 ))}
                              </div>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  {error.message && (
                     <div className="rounded-lg border border-destructive/30 bg-destructive/10 p-4 text-destructive">
                        <p className="text-sm font-medium">{error.message}</p>
                     </div>
                  )}

                  <div className="flex justify-end space-x-4">
                     <Button
                        type="submit"
                        variant="outline"
                        disabled={pending}
                        onClick={form.handleSubmit(onSubmit)}
                        className="min-w-[120px]"
                     >
                        {pending && (
                           <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        )}
                        {pending
                           ? "Saving..."
                           : id
                             ? "Update Draft"
                             : "Save Draft"}
                     </Button>
                     <Button
                        type="button"
                        disabled={pending}
                        onClick={onPublish}
                        className="min-w-[120px]"
                     >
                        {pending && (
                           <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        )}
                        {pending ? "Processing..." : "Publish Post"}
                     </Button>
                  </div>
               </form>
            </Form>
         </div>
      </FormWrapper>
   );
}

export default DraftForm;
