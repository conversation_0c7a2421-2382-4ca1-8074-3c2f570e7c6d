import { Button } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuGroup,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { deleteAuthor } from "@/lib/firebase/authors/actions";
import { Author } from "@/lib/firebase/types";
import { Row } from "@tanstack/react-table";
import { EllipsisIcon, TrashIcon } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

function RowActions({ row }: { row: Row<Author> }) {
   async function handleDelete() {
      toast.promise(
         async () => {
            const result = await deleteAuthor(row.original.id);
            if (!result.success) {
               throw new Error(result.message);
            }
            return result;
         },
         {
            loading: `Deleting ${row.original.name} author...`,
            success: (data) => data.message || "Author successfully deleted",
            error: (err) => err.message || "Error deleting author",
         },
      );
   }

   return (
      <DropdownMenu>
         <DropdownMenuTrigger asChild>
            <div className="flex justify-end">
               <Button
                  size="icon"
                  variant="ghost"
                  className="shadow-none"
                  aria-label="Edit item"
               >
                  <EllipsisIcon size={16} aria-hidden="true" />
               </Button>
            </div>
         </DropdownMenuTrigger>
         <DropdownMenuContent align="end">
            <DropdownMenuGroup>
               <Link href={`/authors/form?id=${row.original.id}`}>
                  <DropdownMenuItem>
                     <span>Edit</span>
                  </DropdownMenuItem>
               </Link>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-destructive focus:text-destructive">
               <TrashIcon className="mr-1 h-4 w-4" />
               <span onClick={handleDelete}>Delete</span>
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
}

export default RowActions;
