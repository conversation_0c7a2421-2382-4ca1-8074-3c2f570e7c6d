"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   <PERSON>,
   CardContent,
   CardDescription,
   Card<PERSON>oot<PERSON>,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import Logo from "@/public/logo.svg";
import { loginFormSchema } from "@/schemas/login-form-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { LockIcon, LogInIcon } from "lucide-react";
import { signIn } from "next-auth/react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

// Infer the type from the schema
type LoginFormValues = z.infer<typeof loginFormSchema>;

export default function LoginForm() {
   const router = useRouter();
   const [isPending, startTransition] = useTransition();

   // Initialize the form with react-hook-form and zod resolver
   const form = useForm<LoginFormValues>({
      resolver: zodResolver(loginFormSchema),
      defaultValues: {
         email: "",
         password: "",
      },
   });

   // Handle form submission
   async function onSubmit(data: LoginFormValues) {
      startTransition(async () => {
         const result = await signIn("credentials", {
            email: data.email,
            password: data.password,
            redirect: false,
         });

         if (result?.error) {
            toast.error("Invalid credentials. Please try again.");
            return;
         }

         toast.success("Login successful!");
         router.push("/dashboard");
      });
   }

   return (
      <div className="flex min-h-svh flex-col px-8 py-6">
         <Image src={Logo} alt="logo" width={100} height={35} priority />

         <div className="-mt-8 flex w-full flex-grow items-center justify-center md:p-10">
            <div className="w-full max-w-lg">
               <div className="flex items-center justify-center py-12 sm:px-6 lg:px-8">
                  <Card className="w-full max-w-md">
                     <CardHeader className="space-y-1">
                        <div className="mb-2 flex justify-center">
                           <div className="rounded-full bg-primary/10 p-3">
                              <LockIcon className="h-6 w-6 text-primary" />
                           </div>
                        </div>
                        <CardTitle className="text-center text-2xl font-bold">
                           Admin Login
                        </CardTitle>
                        <CardDescription className="text-center">
                           Enter your credentials to access the admin panel
                        </CardDescription>
                     </CardHeader>
                     <CardContent>
                        <Form {...form}>
                           <form
                              onSubmit={form.handleSubmit(onSubmit)}
                              className="space-y-4"
                           >
                              <FormField
                                 control={form.control}
                                 name="email"
                                 render={({ field }) => (
                                    <FormItem>
                                       <FormLabel>Email</FormLabel>
                                       <FormControl>
                                          <Input
                                             placeholder="<EMAIL>"
                                             type="email"
                                             autoComplete="email"
                                             {...field}
                                          />
                                       </FormControl>
                                       <FormMessage />
                                    </FormItem>
                                 )}
                              />
                              <FormField
                                 control={form.control}
                                 name="password"
                                 render={({ field }) => (
                                    <FormItem>
                                       <FormLabel>Password</FormLabel>
                                       <FormControl>
                                          <Input
                                             placeholder="••••••••"
                                             type="password"
                                             autoComplete="current-password"
                                             {...field}
                                          />
                                       </FormControl>
                                       <FormMessage />
                                    </FormItem>
                                 )}
                              />
                              <Button
                                 type="submit"
                                 className="mt-2 w-full"
                                 disabled={isPending}
                              >
                                 {isPending ? (
                                    <span className="flex items-center gap-2">
                                       <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                       Logging in...
                                    </span>
                                 ) : (
                                    <span className="flex items-center gap-2">
                                       <LogInIcon className="h-4 w-4" />
                                       Sign in
                                    </span>
                                 )}
                              </Button>
                           </form>
                        </Form>
                     </CardContent>
                     <CardFooter className="flex justify-center">
                        <p className="text-center text-sm text-muted-foreground">
                           Protected area. Unauthorized access is prohibited.
                        </p>
                     </CardFooter>
                  </Card>
               </div>
            </div>
         </div>
      </div>
   );
}
