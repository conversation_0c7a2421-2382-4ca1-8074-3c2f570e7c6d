"use client";

import { Skeleton } from "@/components/ui/skeleton";
import {
   Table,
   TableBody,
   TableCell,
   TableHead,
   TableHeader,
   TableRow,
} from "@/components/ui/table";
import { useIsMobile } from "@/hooks/use-mobile";

interface TableSkeletonProps {
   columnCount: number;
   rowCount?: number;
   hasCheckbox?: boolean;
   hasAvatar?: boolean;
   showFilters?: boolean;
}

export function TableSkeleton({
   columnCount,
   rowCount = 5,
   hasCheckbox = true,
   hasAvatar = true,
   showFilters = true,
}: TableSkeletonProps) {
   const isMobile = useIsMobile();

   // Adjust column count based on presence of checkbox and avatar columns
   // On mobile, limit to fewer columns for better display
   const adjustedColumnCount = isMobile
      ? Math.min(2, columnCount - (hasCheckbox ? 1 : 0) - (hasAvatar ? 1 : 0))
      : columnCount - (hasCheckbox ? 1 : 0) - (hasAvatar ? 1 : 0);

   // Reduce row count on mobile for better performance and display
   const displayRowCount = isMobile ? Math.min(3, rowCount) : rowCount;

   return (
      <div className="max-w-full space-y-4">
         {/* Skeleton for filters */}
         {showFilters && (
            <div className="flex flex-wrap items-center justify-between gap-2">
               <div className="flex items-center gap-2">
                  <Skeleton
                     className={`h-9 ${isMobile ? "w-[150px]" : "w-[250px]"}`}
                  />
                  <Skeleton className="h-9 w-9" />
               </div>
               <div className="flex items-center gap-2">
                  <Skeleton className="h-9 w-[100px]" />
               </div>
            </div>
         )}

         {/* Skeleton for table */}
         <div className="overflow-x-auto overflow-y-hidden rounded-lg border bg-background">
            <Table className="max-w-full table-fixed">
               <TableHeader>
                  <TableRow className="hover:bg-transparent">
                     {/* Checkbox column */}
                     {hasCheckbox && (
                        <TableHead className="h-11 w-[40px]">
                           <Skeleton className="h-4 w-4" />
                        </TableHead>
                     )}

                     {/* Avatar column */}
                     {hasAvatar && (
                        <TableHead className="h-11 w-[50px]"></TableHead>
                     )}

                     {/* Regular columns */}
                     {Array.from({ length: adjustedColumnCount }).map(
                        (_, index) => (
                           <TableHead key={index} className="h-11">
                              <Skeleton className="h-4 w-full max-w-[120px]" />
                           </TableHead>
                        ),
                     )}
                  </TableRow>
               </TableHeader>
               <TableBody>
                  {Array.from({ length: displayRowCount }).map(
                     (_, rowIndex) => (
                        <TableRow key={rowIndex}>
                           {/* Checkbox cell */}
                           {hasCheckbox && (
                              <TableCell>
                                 <Skeleton className="h-4 w-4" />
                              </TableCell>
                           )}

                           {/* Avatar cell */}
                           {hasAvatar && (
                              <TableCell>
                                 <Skeleton className="h-8 w-8 rounded-full" />
                              </TableCell>
                           )}

                           {/* Regular cells */}
                           {Array.from({ length: adjustedColumnCount }).map(
                              (_, cellIndex) => (
                                 <TableCell key={cellIndex}>
                                    <Skeleton className="h-4 w-full" />
                                 </TableCell>
                              ),
                           )}
                        </TableRow>
                     ),
                  )}
               </TableBody>
            </Table>
         </div>

         {/* Skeleton for pagination */}
         <div className={`flex items-center justify-between gap-2`}>
            <Skeleton className="h-9 w-[100px]" />
            <div className="flex items-center gap-2">
               {!isMobile && <Skeleton className="h-9 w-9" />}
               <Skeleton className="h-9 w-9" />
               <Skeleton className="h-9 w-9" />
               {!isMobile && <Skeleton className="h-9 w-9" />}
            </div>
         </div>
      </div>
   );
}
