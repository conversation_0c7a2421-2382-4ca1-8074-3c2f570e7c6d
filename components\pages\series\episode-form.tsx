import { Button } from "@/components/ui/button";
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Episode } from "@/lib/firebase/types";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { useEpisodes } from "./context/episodesProvider";

const formSchema = z.object({
   episodeNumber: z.number().min(1, {
      message: "Episode number must be at least 1.",
   }),
   title: z.string().min(3, {
      message: "Title must be at least 3 characters.",
   }),
   description: z.string().min(10, {
      message: "Description must be at least 10 characters.",
   }),
   episodeLink: z.string().url({
      message: "Please provide a valid URL for the episode.",
   }),
});

type Props = {
   closeDialog: () => void;
   episode?: Episode;
};

function EpisodeForm({ closeDialog, episode }: Props) {
   const { episodes, handleAddEpisode, handleUpdateEpisode } = useEpisodes();

   const form = useForm<z.infer<typeof formSchema>>({
      resolver: zodResolver(formSchema),
      defaultValues: episode
         ? {
              title: episode.title,
              episodeNumber: episode.episodeNumber,
              description: episode.description,
              episodeLink: episode.episodeLink,
           }
         : {
              title: "",
              episodeNumber: 0,
              description: "",
              episodeLink: "",
           },
   });

   function onSubmit(values: z.infer<typeof formSchema>) {
      if (!episode) {
         const existingEpisode = episodes.find(
            (episode) => episode.episodeNumber === values.episodeNumber,
         );

         if (existingEpisode) {
            return toast.error(
               `Episode number ${values.episodeNumber} already exists.`,
            );
         }

         handleAddEpisode(values);
      } else {
         const existingEpisode = episodes.find(
            (episode) => episode.episodeNumber === values.episodeNumber,
         );

         if (
            existingEpisode &&
            existingEpisode.episodeNumber !== episode.episodeNumber
         ) {
            return toast.error(
               `Episode number ${values.episodeNumber} already exists.`,
            );
         }

         handleUpdateEpisode(episode.episodeNumber, values);
      }

      closeDialog();
   }

   return (
      <Form {...form}>
         <form className="w-full space-y-4">
            <FormField
               control={form.control}
               name="episodeNumber"
               render={({ field }) => (
                  <FormItem>
                     <FormLabel>Episode Number</FormLabel>
                     <FormControl>
                        <Input
                           {...field}
                           type="number"
                           placeholder="Episode Number"
                           onChange={(e) =>
                              field.onChange(Number(e.target.value))
                           }
                        />
                     </FormControl>
                     <FormMessage />
                  </FormItem>
               )}
            />

            <FormField
               control={form.control}
               name="title"
               render={({ field }) => (
                  <FormItem>
                     <FormLabel>Episode Title</FormLabel>
                     <FormControl>
                        <Input placeholder="Episode Title" {...field} />
                     </FormControl>
                     <FormMessage />
                  </FormItem>
               )}
            />

            <FormField
               control={form.control}
               name="description"
               render={({ field }) => (
                  <FormItem>
                     <FormLabel>Episode Description</FormLabel>
                     <FormControl>
                        <Input placeholder="Episode Description" {...field} />
                     </FormControl>
                     <FormMessage />
                  </FormItem>
               )}
            />

            <FormField
               control={form.control}
               name="episodeLink"
               render={({ field }) => (
                  <FormItem>
                     <FormLabel>Episode Link</FormLabel>
                     <FormControl>
                        <Input placeholder="Episode Link" {...field} />
                     </FormControl>
                     <FormMessage />
                  </FormItem>
               )}
            />

            <Button onClick={form.handleSubmit(onSubmit)} className="w-full">
               {episode ? "Update" : "Add"}
            </Button>
         </form>
      </Form>
   );
}

export default EpisodeForm;
