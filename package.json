{"name": "pimpim-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/firebase-adapter": "^2.8.0", "@floating-ui/react": "^0.27.7", "@hookform/resolvers": "^4.1.0", "@origin-space/image-cropper": "^0.1.9", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-table": "^8.21.2", "@tiptap/extension-highlight": "^2.11.7", "@tiptap/extension-image": "^2.11.7", "@tiptap/extension-link": "^2.11.7", "@tiptap/extension-subscript": "^2.11.7", "@tiptap/extension-superscript": "^2.11.7", "@tiptap/extension-task-item": "^2.11.7", "@tiptap/extension-task-list": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-typography": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/pm": "^2.11.7", "@tiptap/react": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "firebase": "^11.3.1", "firebase-admin": "^12.7.0", "lucide-react": "^0.475.0", "motion": "^12.6.3", "next": "15.3.2", "next-auth": "^5.0.0-beta.25", "next-themes": "^0.4.4", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.54.2", "sonner": "^2.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "19.1.4", "@types/react-dom": "19.1.5", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8", "prettier": "^3.5.1", "prettier-plugin-tailwindcss": "^0.6.11", "sass": "^1.86.3", "tailwindcss": "^3.4.1", "typescript": "^5"}, "overrides": {"@types/react": "19.1.4", "@types/react-dom": "19.1.5"}}