import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { DataTableWrapper } from "@/components/ui/data-table-wrapper";
import { getDrafts } from "@/lib/firebase/drafts/service";
import { FileTextIcon, PlusIcon } from "lucide-react";
import Link from "next/link";
import { columns } from "./table-columns";

// Separate component for the table content that will be wrapped with Suspense
async function DraftsTableContent() {
   const drafts = await getDrafts();

   return (
      <DataTable
         data={drafts}
         columns={columns}
         defaultSortId="title"
         customButton={
            <>
               <Link href="/posts">
                  <Button variant="outline" className="gap-2">
                     <FileTextIcon size={16} />
                     View Posts
                  </Button>
               </Link>
               <Link href="/posts/drafts/form">
                  <Button className="ml-auto" variant="outline">
                     <PlusIcon
                        className="-ms-1 opacity-60"
                        size={16}
                        aria-hidden="true"
                     />
                     Add New Draft
                  </Button>
               </Link>
            </>
         }
      />
   );
}

async function DraftsPage() {
   return (
      <>
         <div className="flex items-center justify-between pb-4">
            <h1 className="text-2xl font-semibold">Draft Posts</h1>
         </div>

         <DataTableWrapper columnCount={8}>
            <DraftsTableContent />
         </DataTableWrapper>
      </>
   );
}

export default DraftsPage;
