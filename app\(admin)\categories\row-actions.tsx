import { Button } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuGroup,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { deleteCategory } from "@/lib/firebase/categories/actions";
import { Category } from "@/lib/firebase/types";
import { Row } from "@tanstack/react-table";
import { EllipsisIcon, TrashIcon } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

export function RowActions({ row }: { row: Row<Category> }) {
   async function handleDelete() {
      toast.promise(
         async () => {
            const result = await deleteCategory(row.original.id);
            if (!result.success) {
               throw new Error(result.message);
            }
            return result;
         },
         {
            loading: `Deleting ${row.original.name} category...`,
            success: (data) => data.message || "Category successfully deleted",
            error: (err) => err.message || "Error deleting category",
         },
      );
   }

   return (
      <DropdownMenu>
         <DropdownMenuTrigger asChild>
            <div className="flex justify-end">
               <Button
                  size="icon"
                  variant="ghost"
                  className="shadow-none"
                  aria-label="Edit item"
               >
                  <EllipsisIcon size={16} aria-hidden="true" />
               </Button>
            </div>
         </DropdownMenuTrigger>
         <DropdownMenuContent align="end">
            <DropdownMenuGroup>
               <Link href={`/categories/form?id=${row.original.id}`}>
                  <DropdownMenuItem>
                     <span>Edit</span>
                  </DropdownMenuItem>
               </Link>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem
               className="text-destructive focus:text-destructive"
               onClick={handleDelete}
            >
               <TrashIcon className="mr-1 h-4 w-4" />
               <span>Delete</span>
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
}

export default RowActions;
