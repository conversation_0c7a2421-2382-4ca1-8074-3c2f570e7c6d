"use client";

import { type Editor } from "@tiptap/react";
import * as React from "react";

// --- Hooks ---
import { useTiptapEditor } from "@/hooks/use-tiptap-editor";

// --- Icons ---
import { YouTubeIcon } from "@/components/tiptap-editor/tiptap-icons/youtube-icon";

// --- UI Components ---
import {
   EmbedButton,
   EmbedButtonProps,
} from "@/components/tiptap-editor/tiptap-ui/embed-button";

export interface YouTubeEmbedButtonProps
   extends Omit<EmbedButtonProps, "type" | "icon"> {
   editor?: Editor | null;
   text?: string;
}

export const YouTubeEmbedButton = React.forwardRef<
   HTMLButtonElement,
   YouTubeEmbedButtonProps
>(
   (
      {
         editor: providedEditor,
         text,
         className = "",
         disabled,
         onClick,
         children,
         ...buttonProps
      },
      ref,
   ) => {
      const editor = useTiptapEditor(providedEditor);

      if (!editor || !editor.isEditable) {
         return null;
      }

      return (
         <EmbedButton
            ref={ref}
            type="youtube"
            icon={<YouTubeIcon className="tiptap-button-icon" />}
            text={text}
            editor={editor}
            className={className}
            disabled={disabled}
            onClick={onClick}
            {...buttonProps}
         >
            {children}
         </EmbedButton>
      );
   },
);

YouTubeEmbedButton.displayName = "YouTubeEmbedButton";

export default YouTubeEmbedButton;
