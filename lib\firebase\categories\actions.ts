"use server";

import { CategoryFormType } from "@/components/pages/categories/category-form";
import { uploadImage } from "@/lib/cloudinary/cloudinary";
import { slugify } from "@/lib/utils";
import {
   collection,
   deleteDoc,
   doc,
   getDoc,
   getDocs,
   query,
   setDoc,
   updateDoc,
   where,
} from "firebase/firestore";
import { revalidatePath } from "next/cache";
import { db } from "../firebase";

export async function createCategory(data: CategoryFormType) {
   const { name, description, image } = data;

   // Generate a slug from the category name
   const slug = slugify(name);

   // Check if a category with the same name already exists
   const categoriesRef = collection(db, "categories");
   const q = query(categoriesRef, where("name", "==", name));
   const querySnapshot = await getDocs(q);
   if (!querySnapshot.empty) {
      return {
         message: "A category with the same name already exists",
         success: false,
      };
   }

   // Generate a unique ID for the new category document
   const uniqueId = doc(collection(db, "categories")).id;

   // Upload image and get the URL
   const imageUrl =
      image instanceof File && (await uploadImage(image, uniqueId));

   if (!imageUrl) {
      return {
         message: "Image upload failed, please try again!",
         success: false,
      };
   }

   // Create a new category object
   const newCategory = {
      name,
      description,
      slug,
      image: imageUrl,
   };

   const docRef = doc(db, "categories", uniqueId);

   try {
      // Save the new category document to Firestore
      await setDoc(docRef, newCategory);

      revalidatePath("/categories");

      return {
         message: "Category created successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error
               ? error.message
               : "Failed to create category",
         success: false,
      };
   }
}

export async function updateCategory(data: CategoryFormType, id: string) {
   const { name, description, image } = data;

   const slug = slugify(name);

   // Check if a category with the same title already exists
   const movieSnap = await getDoc(doc(db, "categories", id));
   if (!movieSnap.exists()) {
      return {
         message: "Category not found",
         success: false,
      };
   }

   if (!(movieSnap.data().name === name)) {
      const moviesRef = collection(db, "categories");
      const q = query(moviesRef, where("name", "==", name));
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
         return {
            message: "A category with the same name already exists",
            success: false,
         };
      }
   }

   const imageUrl =
      typeof image === "string" ? image : await uploadImage(image, id);

   if (!imageUrl) {
      return {
         message: "Image upload failed, please try again!",
         success: false,
      };
   }

   const docRef = doc(db, "categories", id);

   try {
      await updateDoc(docRef, {
         name,
         description,
         slug,
         image: imageUrl,
      });

      revalidatePath("/categories");

      return {
         message: "Category updated successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error
               ? error.message
               : "Failed to update category",
         success: false,
      };
   }
}

export async function deleteCategory(categoryId: string) {
   if (!categoryId) {
      return {
         message: "Category ID is required",
         success: false,
      };
   }

   try {
      await deleteDoc(doc(db, "categories", categoryId));
      revalidatePath("/categories");

      return {
         message: "Category deleted successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error
               ? error.message
               : "Failed to delete category",
         success: false,
      };
   }
}
