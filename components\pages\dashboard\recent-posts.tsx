"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { formatDistanceToNow } from "date-fns";
import { motion } from "framer-motion";
import { ArrowRightIcon } from "lucide-react";
import Link from "next/link";

interface RecentPostsProps {
   posts: {
      id: string;
      title: string;
      date: Date;
   }[];
}

export function RecentPosts({ posts }: RecentPostsProps) {
   return (
      <motion.div
         initial={{ opacity: 0, y: 20 }}
         animate={{ opacity: 1, y: 0 }}
         transition={{ duration: 0.5, delay: 0.4 }}
      >
         <Card className="h-full">
            <CardHeader>
               <CardTitle>Recent Posts</CardTitle>
               <CardDescription>
                  Latest content added to the platform
               </CardDescription>
            </CardHeader>
            <CardContent>
               <div className="space-y-4">
                  {posts.map((post) => (
                     <div
                        key={post.id}
                        className="flex items-center justify-between"
                     >
                        <div className="space-y-1">
                           <p className="line-clamp-1 font-medium">
                              {post.title}
                           </p>
                           <p className="text-xs text-muted-foreground">
                              {formatDistanceToNow(post.date, {
                                 addSuffix: true,
                              })}
                           </p>
                        </div>
                        <Button variant="ghost" size="icon" asChild>
                           <Link href={`/posts/form?id=${post.id}`}>
                              <ArrowRightIcon className="h-4 w-4" />
                              <span className="sr-only">View post</span>
                           </Link>
                        </Button>
                     </div>
                  ))}
               </div>
               <div className="mt-4 flex justify-end">
                  <Button
                     variant="ghost"
                     size="sm"
                     className="gap-1 text-xs"
                     asChild
                  >
                     <Link href="/posts">
                        View all posts
                        <ArrowRightIcon className="h-3 w-3" />
                     </Link>
                  </Button>
               </div>
            </CardContent>
         </Card>
      </motion.div>
   );
}
