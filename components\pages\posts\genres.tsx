import { Option } from "@/components/ui/multiselect";

export const genres: Option[] = [
   { value: "Action", label: "Action" },
   { value: "Adventure", label: "Adventure" },
   { value: "Animation", label: "Animation" },
   { value: "Biography", label: "Biography" },
   { value: "Comedy", label: "Comedy" },
   { value: "Crime", label: "Crime" },
   { value: "Documentary", label: "Documentary" },
   { value: "Drama", label: "Drama" },
   { value: "Family", label: "Family" },
   { value: "Fantasy", label: "Fantasy" },
   { value: "History", label: "History" },
   { value: "Horror", label: "Horror" },
   { value: "Mystery", label: "Mystery" },
   { value: "Romance", label: "Romance" },
   { value: "Sci-Fi", label: "Sci-Fi" },
   { value: "Thriller", label: "Thriller" },
   { value: "War", label: "War" },
];
