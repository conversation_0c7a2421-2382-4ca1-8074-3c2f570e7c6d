"use client";

import { cn } from "@/lib/utils";
import { Check, X } from "lucide-react";
import { useEffect, useRef, useState, type KeyboardEvent } from "react";

interface TagInputProps {
   placeholder?: string;
   helperText?: string;
   suggestedTags?: string[];
   value?: string[];
   onChange?: (tags: string[]) => void;
   className?: string;
}

export function TagInput({
   placeholder = "Add a tag...",
   helperText = "Press Enter or Comma to add a tag",
   suggestedTags = [],
   value,
   onChange,
   className,
}: TagInputProps) {
   const [tags, setTags] = useState<string[]>(value || []);
   const [inputValue, setInputValue] = useState("");
   const [showDropdown, setShowDropdown] = useState(false);
   const [filteredTags, setFilteredTags] = useState<string[]>([]);
   const [selectedIndex, setSelectedIndex] = useState(-1);
   const inputRef = useRef<HTMLInputElement>(null);
   const dropdownRef = useRef<HTMLDivElement>(null);

   // Update filtered tags when input value changes
   useEffect(() => {
      if (inputValue.trim()) {
         const filtered = suggestedTags.filter(
            (tag) =>
               !tags.includes(tag) &&
               tag.toLowerCase().includes(inputValue.toLowerCase()),
         );
         setFilteredTags(filtered);
         setShowDropdown(filtered.length > 0);
         setSelectedIndex(-1);
      } else {
         setFilteredTags([]);
         setShowDropdown(false);
      }
   }, [inputValue, suggestedTags, tags]);

   // Handle outside click to close dropdown
   useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
         if (
            dropdownRef.current &&
            !dropdownRef.current.contains(event.target as Node) &&
            inputRef.current &&
            !inputRef.current.contains(event.target as Node)
         ) {
            setShowDropdown(false);
         }
      };

      document.addEventListener("mousedown", handleClickOutside);
      return () => {
         document.removeEventListener("mousedown", handleClickOutside);
      };
   }, []);

   // Update parent component when tags change
   useEffect(() => {
      onChange?.(tags);
   }, [tags, onChange]);

   const addTag = (tag: string) => {
      const trimmedTag = tag.trim();
      if (trimmedTag && !tags.includes(trimmedTag)) {
         const newTags = [...tags, trimmedTag];
         setTags(newTags);
         setInputValue("");
         setShowDropdown(false);
      }
   };

   const removeTag = (tagToRemove: string) => {
      const newTags = tags.filter((tag) => tag !== tagToRemove);
      setTags(newTags);
   };

   const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter" || e.key === ",") {
         e.preventDefault();

         if (selectedIndex >= 0 && selectedIndex < filteredTags.length) {
            // Add the selected tag from dropdown
            addTag(filteredTags[selectedIndex]);
         } else if (inputValue) {
            // Add the current input value as a tag
            addTag(inputValue);
         }
      } else if (e.key === "Backspace" && !inputValue && tags.length > 0) {
         // Remove the last tag when backspace is pressed and input is empty
         removeTag(tags[tags.length - 1]);
      } else if (e.key === "ArrowDown" && showDropdown) {
         // Navigate down in dropdown
         e.preventDefault();
         setSelectedIndex((prevIndex) =>
            prevIndex < filteredTags.length - 1 ? prevIndex + 1 : prevIndex,
         );
      } else if (e.key === "ArrowUp" && showDropdown) {
         // Navigate up in dropdown
         e.preventDefault();
         setSelectedIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : 0));
      } else if (e.key === "Escape") {
         // Close dropdown on escape
         setShowDropdown(false);
      }
   };

   return (
      <div className={cn("w-full space-y-1", className)}>
         <div className="relative">
            <div className="flex flex-wrap items-center gap-1 rounded-md border border-input bg-background p-1 focus-within:ring-1 focus-within:ring-ring">
               {tags.map((tag) => (
                  <div
                     key={tag}
                     className="flex items-center gap-1 rounded-md bg-muted px-2 py-1 text-sm"
                  >
                     <span>{tag}</span>
                     <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="text-muted-foreground hover:text-foreground"
                        aria-label={`Remove ${tag}`}
                     >
                        <X className="h-3 w-3" />
                     </button>
                  </div>
               ))}
               <input
                  ref={inputRef}
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  onFocus={() => inputValue && setShowDropdown(true)}
                  className="flex-1 bg-transparent px-2 py-1 text-sm outline-none placeholder:text-muted-foreground"
                  placeholder={tags.length === 0 ? placeholder : ""}
               />
            </div>

            {showDropdown && (
               <div
                  ref={dropdownRef}
                  className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border border-input bg-background shadow-md"
               >
                  {filteredTags.map((tag, index) => (
                     <div
                        key={tag}
                        onClick={() => {
                           addTag(tag);
                           inputRef.current?.focus();
                        }}
                        className={cn(
                           "flex cursor-pointer items-center px-3 py-2 text-sm hover:bg-muted",
                           selectedIndex === index && "bg-muted",
                        )}
                     >
                        <div className="mr-2 w-4">
                           {tags.includes(tag) && <Check className="h-4 w-4" />}
                        </div>
                        {tag}
                     </div>
                  ))}
               </div>
            )}
         </div>

         {helperText && (
            <p className="text-xs text-muted-foreground">{helperText}</p>
         )}
      </div>
   );
}
