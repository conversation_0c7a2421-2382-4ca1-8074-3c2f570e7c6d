"use server";

import { uploadImage } from "@/lib/cloudinary/cloudinary";
import { slugify } from "@/lib/utils";
import { UserFormType } from "@/schemas/user-form-schema";
import bcryptjs from "bcryptjs";
import {
   collection,
   deleteDoc,
   doc,
   getDoc,
   getDocs,
   query,
   setDoc,
   Timestamp,
   updateDoc,
   where,
} from "firebase/firestore";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { db } from "../firebase";
import { Profile, User } from "../types";

export const createUser = async (data: UserFormType) => {
   const { name, email, password, role, image, isNewUser } = data;

   // Only require password for new users
   if (isNewUser && !password) {
      return {
         message: "Password is required for new users",
         success: false,
      };
   }

   // Check if user with the same email already exists
   const existedUser = await getDocs(
      query(collection(db, "users"), where("email", "==", email)),
   );
   if (!existedUser.empty) {
      return {
         message: "A user with the same email already exists",
         success: false,
      };
   }

   // Hash the password if provided
   let hash = "";
   if (password) {
      hash = await bcryptjs.hash(password, 10);
   }

   // Generate a unique ID for the new user
   const uniqueId = doc(collection(db, "users")).id;

   // Upload image if provided
   let imageUrl = "";
   if (image) {
      imageUrl =
         image instanceof File ? await uploadImage(image, uniqueId) : "";
      if (!imageUrl && image instanceof File) {
         return {
            message: "Image upload failed, please try again!",
            success: false,
         };
      }
   }

   // Create user object
   const user: User = {
      id: uniqueId,
      name,
      email,
      role: role || "user",
      ...(imageUrl ? { image: imageUrl } : {}),
   };

   // Save user to Firestore
   const userRef = doc(db, "users", uniqueId);
   await setDoc(userRef, {
      ...user,
      password: hash,
      emailVerified: false,
   });

   // Generate unique username
   const uniqueUsername = await generateUniqueUsername(name);

   await createUserProfile(uniqueId, {
      id: uniqueId,
      username: uniqueUsername,
      displayName: name,
      email: email,
   });

   revalidatePath("/users");

   // Return success message before redirect
   const result = { message: "User created successfully", success: true };
   redirect("/users");

   return result; // This line is only reached if redirect fails
};

export const updateUser = async (data: UserFormType, id: string) => {
   if (!id) return { message: "User ID is required", success: false };

   const { name, email, password, role, image } = data;

   // Check for existing email but exclude current user
   const existedUser = await getDocs(
      query(collection(db, "users"), where("email", "==", email)),
   );

   // Only check for duplicate email if it exists and doesn't belong to current user
   const duplicateEmail = existedUser.docs.some((doc) => doc.id !== id);
   if (duplicateEmail) {
      return {
         message: "A user with the same email already exists",
         success: false,
      };
   }

   // Upload image if provided
   let imageUrl = "";
   if (image) {
      imageUrl =
         typeof image === "string" ? image : await uploadImage(image, id);
      if (!imageUrl && image instanceof File) {
         return {
            message: "Image upload failed, please try again!",
            success: false,
         };
      }
   }

   // Create update object for user
   const userUpdateData: Partial<User> = {
      name,
      email,
      role: role || "user",
   };

   // Only update password if provided
   if (password) {
      const hash = await bcryptjs.hash(password, 10);
      await updateDoc(doc(db, "users", id), { password: hash });
   }

   // Only update image if provided
   if (imageUrl) {
      userUpdateData.image = imageUrl;
   }

   // Update user in Firestore
   const userRef = doc(db, "users", id);
   await updateDoc(userRef, userUpdateData);

   // Check if profile exists and update email and displayName
   const profileRef = doc(db, "profiles", id);
   const profileSnap = await getDoc(profileRef);

   if (profileSnap.exists()) {
      await updateDoc(profileRef, { email, displayName: name });
   } else {
      const uniqueUsername = await generateUniqueUsername(name);

      await createUserProfile(id, {
         id,
         email,
         username: uniqueUsername,
         displayName: name,
      });
   }

   revalidatePath("/users");

   // Return success message before redirect
   const result = { message: "User updated successfully", success: true };
   redirect("/users");

   return result; // This line is only reached if redirect fails
};

export const deleteUser = async (id: string) => {
   if (!id) return { message: "User ID is required", success: false };

   try {
      // Delete user
      const userRef = doc(db, "users", id);
      await deleteDoc(userRef);

      // Delete profile if exists
      const profileRef = doc(db, "profiles", id);
      const profileSnap = await getDoc(profileRef);
      if (profileSnap.exists()) {
         await deleteDoc(profileRef);
      }

      // Delete account if exists
      const accountsQuery = query(
         collection(db, "accounts"),
         where("userId", "==", id),
      );
      const accountsSnap = await getDocs(accountsQuery);
      if (!accountsSnap.empty) {
         await deleteDoc(accountsSnap.docs[0].ref);
      }

      // Delete all sessions for this user (log them out)
      await logoutUser(id);

      revalidatePath("/users");

      return { message: "User deleted successfully", success: true };
   } catch (error) {
      console.error("Error deleting user:", error);
      return { message: "Error deleting user", success: false };
   }
};

export async function createUserProfile(
   userId: string,
   profileData: Profile,
): Promise<void> {
   const { username } = profileData;

   // Generate unique username
   const uniqueUsername = await generateUniqueUsername(username);

   try {
      const profileDocRef = doc(db, "profiles", userId);
      await setDoc(
         profileDocRef,
         {
            ...profileData,
            username: uniqueUsername,
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
         },
         { merge: true },
      );
   } catch (error) {
      console.error("Error creating user profile:", error);
      throw error;
   }
}

export const updateUserProfile = async (
   profileData: Partial<Profile>,
   id: string,
) => {
   if (!id) return { message: "User ID is required", success: false };

   // Check if profile exists
   const profileRef = doc(db, "profiles", id);
   const profileSnap = await getDoc(profileRef);

   if (!profileSnap.exists()) {
      return { message: "Profile not found", success: false };
   }

   // Prepare the updated profile data

   const updatedProfile = {
      ...profileData,
      updatedAt: new Date(),
   };

   // Update the profile
   await updateDoc(profileRef, updatedProfile);

   revalidatePath("/users");

   return { message: "User profile updated successfully", success: true };
};

export async function generateUniqueUsername(
   displayName: string,
): Promise<string> {
   // Generate the base slug from the display name
   const base = slugify(displayName);
   let username = base;
   let counter = 1;

   // Check if the base slug is already taken
   while (await isUsernameTaken(username)) {
      username = `${base}${counter}`;
      counter++;
   }

   return username;
}

export async function isUsernameTaken(username: string): Promise<boolean> {
   const querySnapshot = await getDocs(
      query(collection(db, "profiles"), where("username", "==", username)),
   );
   return !querySnapshot.empty;
}

export const updateUserRole = async (role: string, id: string) => {
   if (!id) return { message: "User ID is required", success: false };
   if (!["user", "admin", "editor"].includes(role)) {
      return { message: "Invalid role", success: false };
   }

   const docRef = doc(collection(db, "users"), id);
   await updateDoc(docRef, { role });

   revalidatePath("/users");

   return { message: "User role updated successfully", success: true };
};

/**
 * Logs out a user by deleting all their sessions
 * @param userId The ID of the user to log out
 * @returns A message indicating success or failure
 */
export const logoutUser = async (userId: string) => {
   if (!userId) return { message: "User ID is required", success: false };

   try {
      // Find all sessions for this user
      const sessionsQuery = query(
         collection(db, "sessions"),
         where("userId", "==", userId),
      );
      const sessionsSnapshot = await getDocs(sessionsQuery);

      // Delete all sessions for this user
      const deletePromises = sessionsSnapshot.docs.map((sessionDoc) =>
         deleteDoc(sessionDoc.ref),
      );
      await Promise.all(deletePromises);

      revalidatePath("/users");

      return {
         message: `User logged out successfully from ${sessionsSnapshot.size} session(s)`,
         success: true,
      };
   } catch (error) {
      console.error("Error logging out user:", error);
      return {
         message: "Failed to log out user",
         success: false,
      };
   }
};
