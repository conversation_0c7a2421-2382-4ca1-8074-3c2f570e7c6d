"use client";

import { type Editor } from "@tiptap/react";
import React from "react";

// --- Hooks ---
import { useTiptapEditor } from "@/hooks/use-tiptap-editor";

// --- UI Primitives ---
import {
   Button,
   ButtonProps,
} from "@/components/tiptap-editor/tiptap-ui-primitive/button";

export type EmbedType = "youtube" | "instagram" | "twitter";

export interface EmbedButtonProps extends Omit<ButtonProps, "type"> {
   editor?: Editor | null;
   type: EmbedType;
   text?: string;
   icon: React.ReactNode;
}

export function insertEmbed(editor: Editor | null, type: EmbedType): boolean {
   if (!editor) return false;

   const extensionName = `${type}Embed`;

   return editor
      .chain()
      .focus()
      .insertContent({
         type: extensionName,
         attrs: { src: "" },
      })
      .run();
}

export function useEmbedButton(
   editor: Editor | null,
   type: EmbedType,
   disabled: boolean = false,
) {
   const handleInsertEmbed = React.useCallback(() => {
      if (disabled) return false;
      return insertEmbed(editor, type);
   }, [editor, type, disabled]);

   return {
      handleInsertEmbed,
   };
}

export const EmbedButton = React.forwardRef<
   HTMLButtonElement,
   EmbedButtonProps
>(
   (
      {
         editor: providedEditor,
         type,
         text,
         icon,
         className = "",
         disabled,
         onClick,
         children,
         ...buttonProps
      },
      ref,
   ) => {
      const editor = useTiptapEditor(providedEditor);
      const { handleInsertEmbed } = useEmbedButton(editor, type, disabled);

      const handleClick = React.useCallback(
         (e: React.MouseEvent<HTMLButtonElement>) => {
            onClick?.(e);

            if (!e.defaultPrevented && !disabled) {
               handleInsertEmbed();
            }
         },
         [onClick, disabled, handleInsertEmbed],
      );

      if (!editor || !editor.isEditable) {
         return null;
      }

      return (
         <Button
            ref={ref}
            type="button"
            className={className.trim()}
            data-style="ghost"
            role="button"
            tabIndex={-1}
            aria-label={`Add ${type} embed`}
            tooltip={`Add ${type} embed`}
            onClick={handleClick}
            {...buttonProps}
         >
            {children || (
               <>
                  {icon}
                  {text && <span className="tiptap-button-text">{text}</span>}
               </>
            )}
         </Button>
      );
   },
);

EmbedButton.displayName = "EmbedButton";

export default EmbedButton;
