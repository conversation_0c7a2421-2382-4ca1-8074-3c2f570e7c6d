"use client";

import { ReactNode, Suspense } from "react";
import { TableSkeleton } from "./table-skeleton";

interface DataTableWrapperProps {
   children: ReactNode;
   columnCount: number;
   rowCount?: number;
   hasCheckbox?: boolean;
   hasAvatar?: boolean;
   showFilters?: boolean;
}

export function DataTableWrapper({
   children,
   columnCount,
   rowCount = 5,
   hasCheckbox = true,
   hasAvatar = true,
   showFilters = true,
}: DataTableWrapperProps) {
   return (
      <Suspense
         fallback={
            <TableSkeleton
               columnCount={columnCount}
               rowCount={rowCount}
               hasCheckbox={hasCheckbox}
               hasAvatar={hasAvatar}
               showFilters={showFilters}
            />
         }
      >
         {children}
      </Suspense>
   );
}
