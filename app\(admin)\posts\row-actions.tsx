import { Button } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuGroup,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { convertPostToDraft } from "@/lib/firebase/drafts/actions";
import { deletePost } from "@/lib/firebase/posts/actions";
import { Post } from "@/lib/firebase/types";
import { Row } from "@tanstack/react-table";
import {
   EllipsisIcon,
   FileTextIcon,
   PencilIcon,
   TrashIcon,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

export function RowActions({ row }: { row: Row<Post> }) {
   async function handleDelete() {
      toast.promise(deletePost(row.original.id), {
         loading: `Deleting ${row.original.title} post...`,
         success: "Post successfully deleted",
         error: "Error deleting post",
      });
   }

   async function handleConvertToDraft() {
      toast.promise(convertPostToDraft(row.original.id), {
         loading: `Converting ${row.original.title} to draft...`,
         success: "Post successfully converted to draft",
         error: "Error converting post to draft",
      });
   }

   return (
      <DropdownMenu>
         <DropdownMenuTrigger asChild>
            <div className="flex justify-end">
               <Button
                  size="icon"
                  variant="ghost"
                  className="shadow-none"
                  aria-label="Edit item"
               >
                  <EllipsisIcon size={16} aria-hidden="true" />
               </Button>
            </div>
         </DropdownMenuTrigger>
         <DropdownMenuContent align="end">
            <DropdownMenuGroup>
               <Link href={`/posts/form?id=${row.original.id}`}>
                  <DropdownMenuItem>
                     <PencilIcon className="mr-1 h-4 w-4" />
                     <span>Edit</span>
                  </DropdownMenuItem>
               </Link>
               <DropdownMenuItem onClick={handleConvertToDraft}>
                  <FileTextIcon className="mr-1 h-4 w-4" />
                  <span>Convert to Draft</span>
               </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem
               className="text-destructive focus:text-destructive"
               onClick={handleDelete}
            >
               <TrashIcon className="mr-1 h-4 w-4" />
               <span>Delete</span>
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
}
