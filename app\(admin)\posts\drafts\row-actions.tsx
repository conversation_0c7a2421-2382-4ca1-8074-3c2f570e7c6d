import { Button } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuGroup,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { deleteDraft, publishDraft } from "@/lib/firebase/drafts/actions";
import { Draft } from "@/lib/firebase/types";
import { Row } from "@tanstack/react-table";
import { EllipsisIcon, PencilIcon, TrashIcon, UploadIcon } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

export function RowActions({ row }: { row: Row<Draft> }) {
   async function handleDelete() {
      toast.promise(deleteDraft(row.original.id), {
         loading: `Deleting draft...`,
         success: "Draft successfully deleted",
         error: "Error deleting draft",
      });
   }

   async function handlePublish() {
      toast.promise(publishDraft(row.original.id), {
         loading: `Publishing draft...`,
         success: "Draft successfully published",
         error: (err) => `Error publishing draft: ${err.message}`,
      });
   }

   return (
      <DropdownMenu>
         <DropdownMenuTrigger asChild>
            <Button
               variant="ghost"
               className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
            >
               <EllipsisIcon className="h-4 w-4" />
               <span className="sr-only">Open menu</span>
            </Button>
         </DropdownMenuTrigger>
         <DropdownMenuContent align="end" className="w-[160px]">
            <DropdownMenuGroup>
               <DropdownMenuItem asChild>
                  <Link
                     href={`/posts/drafts/form?id=${row.original.id}`}
                     className="flex cursor-pointer items-center"
                  >
                     <PencilIcon className="mr-2 h-4 w-4" />
                     Edit
                  </Link>
               </DropdownMenuItem>
               <DropdownMenuItem
                  onClick={handlePublish}
                  className="flex cursor-pointer items-center"
               >
                  <UploadIcon className="mr-2 h-4 w-4" />
                  Publish
               </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem
               onClick={handleDelete}
               className="flex cursor-pointer items-center text-destructive focus:text-destructive"
            >
               <TrashIcon className="mr-2 h-4 w-4" />
               Delete
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
}
