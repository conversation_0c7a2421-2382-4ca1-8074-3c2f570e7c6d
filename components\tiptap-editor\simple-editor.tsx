"use client";

import { <PERSON><PERSON><PERSON><PERSON>, Editor<PERSON>ontex<PERSON>, useEditor } from "@tiptap/react";
import * as React from "react";

// --- Tiptap Core Extensions ---
import { Highlight } from "@tiptap/extension-highlight";
import { Image } from "@tiptap/extension-image";
import { TaskItem } from "@tiptap/extension-task-item";
import { TaskList } from "@tiptap/extension-task-list";
import { TextAlign } from "@tiptap/extension-text-align";
import { Typography } from "@tiptap/extension-typography";
import { Underline } from "@tiptap/extension-underline";
import { StarterKit } from "@tiptap/starter-kit";

// --- Custom Extensions ---
import { InstagramEmbedExtension } from "@/components/tiptap-editor/tiptap-extension/instagram-embed-extension";
import { Link } from "@/components/tiptap-editor/tiptap-extension/link-extension";
import { Selection } from "@/components/tiptap-editor/tiptap-extension/selection-extension";
import { TrailingNode } from "@/components/tiptap-editor/tiptap-extension/trailing-node-extension";
import { TwitterEmbedExtension } from "@/components/tiptap-editor/tiptap-extension/twitter-embed-extension";
import { YouTubeEmbedExtension } from "@/components/tiptap-editor/tiptap-extension/youtube-embed-extension";

// --- UI Primitives ---
import { Button } from "@/components/tiptap-editor/tiptap-ui-primitive/button";
import { Spacer } from "@/components/tiptap-editor/tiptap-ui-primitive/spacer";
import {
   Toolbar,
   ToolbarGroup,
   ToolbarSeparator,
} from "@/components/tiptap-editor/tiptap-ui-primitive/toolbar";

// --- Tiptap Node ---
import "@/components/tiptap-editor/tiptap-node/image-node/image-node.scss";
import { ImageUploadNode } from "@/components/tiptap-editor/tiptap-node/image-upload-node/image-upload-node-extension";
import "@/components/tiptap-editor/tiptap-node/instagram-embed-node/instagram-embed-node.scss";
import "@/components/tiptap-editor/tiptap-node/list-node/list-node.scss";
import "@/components/tiptap-editor/tiptap-node/paragraph-node/paragraph-node.scss";
import "@/components/tiptap-editor/tiptap-node/twitter-embed-node/twitter-embed-node.scss";
import "@/components/tiptap-editor/tiptap-node/youtube-embed-node/youtube-embed-node.scss";

// --- Tiptap UI ---
import { FullscreenButton } from "@/components/tiptap-editor/tiptap-ui/fullscreen-button";
import { HeadingDropdownMenu } from "@/components/tiptap-editor/tiptap-ui/heading-dropdown-menu";
import {
   HighlightContent,
   HighlighterButton,
   HighlightPopover,
} from "@/components/tiptap-editor/tiptap-ui/highlight-popover";
import { ImageUploadButton } from "@/components/tiptap-editor/tiptap-ui/image-upload-button";
import { InstagramEmbedButton } from "@/components/tiptap-editor/tiptap-ui/instagram-embed-button";
import {
   LinkButton,
   LinkContent,
   LinkPopover,
} from "@/components/tiptap-editor/tiptap-ui/link-popover";
import { ListDropdownMenu } from "@/components/tiptap-editor/tiptap-ui/list-dropdown-menu";
import { MarkButton } from "@/components/tiptap-editor/tiptap-ui/mark-button";
import { NodeButton } from "@/components/tiptap-editor/tiptap-ui/node-button";
import { TextAlignButton } from "@/components/tiptap-editor/tiptap-ui/text-align-button";
import { TwitterEmbedButton } from "@/components/tiptap-editor/tiptap-ui/twitter-embed-button";
import { UndoRedoButton } from "@/components/tiptap-editor/tiptap-ui/undo-redo-button";
import { YouTubeEmbedButton } from "@/components/tiptap-editor/tiptap-ui/youtube-embed-button";

// --- Icons ---
import { ArrowLeftIcon } from "@/components/tiptap-editor/tiptap-icons/arrow-left-icon";
import { HighlighterIcon } from "@/components/tiptap-editor/tiptap-icons/highlighter-icon";
import { LinkIcon } from "@/components/tiptap-editor/tiptap-icons/link-icon";

// --- Hooks ---
import { useFullscreen } from "@/hooks/use-fullscreen";
import { useIsMobile } from "@/hooks/use-mobile";
import { useWindowSize } from "@/hooks/use-window-size";

// --- Components ---
import { ThemeToggle } from "@/components/tiptap-editor/theme-toggle";

// --- Lib ---
import { MAX_FILE_SIZE } from "@/lib/tiptap-utils";
import { handleCloudinaryImageUpload } from "@/lib/tiptap-utils/image-upload";

// --- Styles ---
import "@/components/tiptap-editor/simple-editor.scss";

const MainToolbarContent = ({
   onHighlighterClick,
   onLinkClick,
   isMobile,
}: {
   onHighlighterClick: () => void;
   onLinkClick: () => void;
   isMobile: boolean;
}) => {
   return (
      <>
         <Spacer />

         <ToolbarGroup>
            <UndoRedoButton action="undo" />
            <UndoRedoButton action="redo" />
         </ToolbarGroup>

         <ToolbarSeparator />

         <ToolbarGroup>
            <HeadingDropdownMenu levels={[1, 2, 3, 4]} />
            <ListDropdownMenu
               types={["bulletList", "orderedList", "taskList"]}
            />
            <NodeButton type="blockquote" />
         </ToolbarGroup>

         <ToolbarSeparator />

         <ToolbarGroup>
            <MarkButton type="bold" />
            <MarkButton type="italic" />
            <MarkButton type="strike" />
            <MarkButton type="underline" />
            {!isMobile ? (
               <HighlightPopover />
            ) : (
               <HighlighterButton onClick={onHighlighterClick} />
            )}
            {!isMobile ? <LinkPopover /> : <LinkButton onClick={onLinkClick} />}
         </ToolbarGroup>

         <ToolbarSeparator />

         <ToolbarGroup>
            <TextAlignButton align="left" />
            <TextAlignButton align="center" />
            <TextAlignButton align="right" />
            <TextAlignButton align="justify" />
         </ToolbarGroup>

         <ToolbarSeparator />

         <ToolbarGroup>
            <ImageUploadButton />
            <YouTubeEmbedButton />
            <InstagramEmbedButton />
            <TwitterEmbedButton />
         </ToolbarGroup>

         <Spacer />

         {isMobile && <ToolbarSeparator />}

         <ToolbarGroup>
            <ThemeToggle />
         </ToolbarGroup>
      </>
   );
};

const MobileToolbarContent = ({
   type,
   onBack,
}: {
   type: "highlighter" | "link";
   onBack: () => void;
}) => (
   <>
      <ToolbarGroup>
         <Button data-style="ghost" onClick={onBack}>
            <ArrowLeftIcon className="tiptap-button-icon" />
            {type === "highlighter" ? (
               <HighlighterIcon className="tiptap-button-icon" />
            ) : (
               <LinkIcon className="tiptap-button-icon" />
            )}
         </Button>
      </ToolbarGroup>

      <ToolbarSeparator />

      {type === "highlighter" ? <HighlightContent /> : <LinkContent />}
   </>
);

type Props = {
   onChange: (html: string) => void;
   initialContent: string;
};

export function SimpleEditor({ onChange, initialContent }: Props) {
   const isMobile = useIsMobile();
   const windowSize = useWindowSize();
   const { isFullscreen, toggleFullscreen } = useFullscreen();
   const [mobileView, setMobileView] = React.useState<
      "main" | "highlighter" | "link"
   >("main");
   const [rect, setRect] = React.useState<
      Pick<DOMRect, "x" | "y" | "width" | "height">
   >({
      x: 0,
      y: 0,
      width: 0,
      height: 0,
   });
   const toolbarRef = React.useRef<HTMLDivElement>(null);
   const containerRef = React.useRef<HTMLDivElement>(null);

   React.useEffect(() => {
      const updateRect = () => {
         setRect(document.body.getBoundingClientRect());
      };

      updateRect();

      const resizeObserver = new ResizeObserver(updateRect);
      resizeObserver.observe(document.body);

      window.addEventListener("scroll", updateRect);

      return () => {
         resizeObserver.disconnect();
         window.removeEventListener("scroll", updateRect);
      };
   }, []);

   const editor = useEditor({
      immediatelyRender: false,
      content: "<p></p>",
      editorProps: {
         attributes: {
            autocomplete: "off",
            autocorrect: "off",
            autocapitalize: "off",
            "aria-label": "Main content area, start typing to enter text.",
         },
      },
      extensions: [
         StarterKit,
         TextAlign.configure({ types: ["heading", "paragraph"] }),
         Underline,
         TaskList,
         TaskItem.configure({ nested: true }),
         Highlight.configure({ multicolor: true }),
         Image,
         Typography,

         Selection,
         ImageUploadNode.configure({
            accept: "image/*",
            maxSize: MAX_FILE_SIZE,
            limit: 3,
            upload: handleCloudinaryImageUpload,
            onError: (error) => {
               console.error("Upload failed:", error);
               // Toast notification is already handled in the upload function
            },
            onSuccess: (url) => {
               console.log("Image uploaded successfully:", url);
               // Toast notification is already handled in the upload function
            },
         }),
         TrailingNode,
         Link.configure({ openOnClick: false }),
         YouTubeEmbedExtension,
         InstagramEmbedExtension,
         TwitterEmbedExtension,
      ],
      onUpdate: ({ editor }) => {
         onChange(editor.getHTML());
      },
   });

   // Update editor content when initialContent changes
   React.useEffect(() => {
      if (editor && initialContent) {
         // Use setTimeout to avoid React's flushSync error
         setTimeout(() => {
            editor.commands.setContent(initialContent);
         }, 0);
      }
   }, [editor, initialContent]);

   React.useEffect(() => {
      const checkCursorVisibility = () => {
         if (!editor || !toolbarRef.current) return;

         const { state, view } = editor;
         if (!view.hasFocus()) return;

         const { from } = state.selection;
         const cursorCoords = view.coordsAtPos(from);

         if (windowSize.height < rect.height) {
            if (cursorCoords && toolbarRef.current) {
               const toolbarHeight =
                  toolbarRef.current.getBoundingClientRect().height;
               const isEnoughSpace =
                  windowSize.height - cursorCoords.top - toolbarHeight > 0;

               // If not enough space, scroll until the cursor is the middle of the screen
               if (!isEnoughSpace) {
                  const scrollY =
                     cursorCoords.top - windowSize.height / 2 + toolbarHeight;
                  window.scrollTo({
                     top: scrollY,
                     behavior: "smooth",
                  });
               }
            }
         }
      };

      checkCursorVisibility();
   }, [editor, rect.height, windowSize.height]);

   React.useEffect(() => {
      if (!isMobile && mobileView !== "main") {
         setMobileView("main");
      }
   }, [isMobile, mobileView]);

   return (
      <div
         ref={containerRef}
         className={`simple-editor-container ${isFullscreen ? "fullscreen" : ""}`}
      >
         <EditorContext.Provider value={{ editor }}>
            <Toolbar ref={toolbarRef}>
               {mobileView === "main" ? (
                  <MainToolbarContent
                     onHighlighterClick={() => setMobileView("highlighter")}
                     onLinkClick={() => setMobileView("link")}
                     isMobile={isMobile}
                  />
               ) : (
                  <MobileToolbarContent
                     type={
                        mobileView === "highlighter" ? "highlighter" : "link"
                     }
                     onBack={() => setMobileView("main")}
                  />
               )}
            </Toolbar>

            <div className="content-wrapper">
               <FullscreenButton
                  isFullscreen={isFullscreen}
                  onToggleFullscreen={toggleFullscreen}
                  className="fullscreen-button"
                  data-style="ghost"
                  aria-label={
                     isFullscreen ? "Exit fullscreen" : "Enter fullscreen"
                  }
               />
               <EditorContent
                  editor={editor}
                  role="presentation"
                  className="simple-editor-content"
               />
            </div>
         </EditorContext.Provider>
      </div>
   );
}
