"use client";

import { useCallback, useEffect, useRef, useState } from "react";

type AutoSaveOptions = {
   /**
    * The key to use for storing data in localStorage
    */
   key: string;

   /**
    * Debounce delay in milliseconds
    * @default 2000
    */
   delay?: number;

   /**
    * Whether to enable auto-save
    * @default true
    */
   enabled?: boolean;
};

/**
 * Hook for manually saving form data to localStorage with debouncing
 */
export function useAutoSave<T>(options: AutoSaveOptions) {
   const { key, delay = 2000, enabled = true } = options;
   const [isSaving, setIsSaving] = useState(false);
   const [isSaved, setIsSaved] = useState(false);
   const timerRef = useRef<NodeJS.Timeout | null>(null);
   const [isClient, setIsClient] = useState(false);

   // Set isClient to true once the component is mounted
   useEffect(() => {
      setIsClient(true);
   }, []);

   // Clear any existing timers when component unmounts
   useEffect(() => {
      return () => {
         if (timerRef.current) {
            clearTimeout(timerRef.current);
         }
      };
   }, []);

   // Save data to localStorage with debouncing
   const saveData = useCallback(
      (data: T) => {
         if (!enabled || !isClient) return;

         // Clear any existing timer
         if (timerRef.current) {
            clearTimeout(timerRef.current);
         }

         // Set saving state
         setIsSaving(true);
         setIsSaved(false);

         // Create a new timer
         timerRef.current = setTimeout(() => {
            try {
               localStorage.setItem(key, JSON.stringify(data));
               setIsSaving(false);
               setIsSaved(true);

               // Reset saved state after a delay
               setTimeout(() => {
                  setIsSaved(false);
               }, 2000);
            } catch (error) {
               console.error("Failed to save data to localStorage:", error);
               setIsSaving(false);
            }
         }, delay);
      },
      [key, delay, enabled, isClient],
   );

   // Load data from localStorage
   const loadSavedData = useCallback((): T | null => {
      if (!isClient) return null;

      try {
         const savedData = localStorage.getItem(key);
         return savedData ? JSON.parse(savedData) : null;
      } catch (error) {
         console.error("Failed to load data from localStorage:", error);
         return null;
      }
   }, [key, isClient]);

   // Clear data from localStorage
   const clearSavedData = useCallback(() => {
      if (!isClient) return;

      try {
         localStorage.removeItem(key);
         // Clear any existing timer
         if (timerRef.current) {
            clearTimeout(timerRef.current);
            timerRef.current = null;
         }
         setIsSaving(false);
         setIsSaved(false);
      } catch (error) {
         console.error("Failed to clear data from localStorage:", error);
      }
   }, [key, isClient]);

   return {
      saveData,
      loadSavedData,
      clearSavedData,
      isSaving,
      isSaved,
      isReady: isClient,
   };
}

// "use client";

// import { useCallback, useEffect, useRef, useState } from "react";

// type AutoSaveOptions = {
//    /**
//     * The key to use for storing data in localStorage
//     */
//    key: string;

//    /**
//     * Debounce delay in milliseconds
//     * @default 2000
//     */
//    delay?: number;

//    /**
//     * Whether to enable auto-save
//     * @default true
//     */
//    enabled?: boolean;
// };

// // Helper to check if localStorage is available
// const isLocalStorageAvailable = () => {
//    if (typeof window === "undefined") return false;

//    try {
//       // Test localStorage availability
//       const testKey = "__test_storage__";
//       localStorage.setItem(testKey, testKey);
//       localStorage.removeItem(testKey);
//       return true;
//    } catch {
//       return false;
//    }
// };

// /**
//  * Hook for manually saving form data to localStorage with debouncing
//  */
// export function useAutoSave<T>(options: AutoSaveOptions) {
//    const { key, delay = 2000, enabled = true } = options;
//    const [isSaving, setIsSaving] = useState(false);
//    const [isSaved, setIsSaved] = useState(false);
//    const timerRef = useRef<NodeJS.Timeout | null>(null);
//    const [isClient, setIsClient] = useState(false);

//    // Set isClient to true once the component is mounted
//    useEffect(() => {
//       setIsClient(true);
//    }, []);

//    // Clear any existing timers when component unmounts
//    useEffect(() => {
//       return () => {
//          if (timerRef.current) {
//             clearTimeout(timerRef.current);
//          }
//       };
//    }, []);

//    // Save data to localStorage with debouncing
//    const saveData = useCallback(
//       (data: T) => {
//          if (!enabled || !isClient || !isLocalStorageAvailable()) return;

//          // Clear any existing timer
//          if (timerRef.current) {
//             clearTimeout(timerRef.current);
//          }

//          // Set saving state
//          setIsSaving(true);
//          setIsSaved(false);

//          // Create a new timer
//          timerRef.current = setTimeout(() => {
//             try {
//                localStorage.setItem(key, JSON.stringify(data));
//                setIsSaving(false);
//                setIsSaved(true);

//                // Reset saved state after a delay
//                setTimeout(() => {
//                   setIsSaved(false);
//                }, 2000);
//             } catch (error) {
//                console.error("Failed to save data to localStorage:", error);
//                setIsSaving(false);
//             }
//          }, delay);
//       },
//       [key, delay, enabled, isClient],
//    );

//    // Load data from localStorage
//    const loadSavedData = useCallback((): T | null => {
//       if (!isClient || !isLocalStorageAvailable()) return null;

//       try {
//          const savedData = localStorage.getItem(key);
//          return savedData ? JSON.parse(savedData) : null;
//       } catch (error) {
//          console.error("Failed to load data from localStorage:", error);
//          return null;
//       }
//    }, [key, isClient]);

//    // Clear data from localStorage
//    const clearSavedData = useCallback(() => {
//       if (!isClient || !isLocalStorageAvailable()) return;

//       try {
//          localStorage.removeItem(key);
//          // Clear any existing timer
//          if (timerRef.current) {
//             clearTimeout(timerRef.current);
//             timerRef.current = null;
//          }
//          setIsSaving(false);
//          setIsSaved(false);
//       } catch (error) {
//          console.error("Failed to clear data from localStorage:", error);
//       }
//    }, [key, isClient]);

//    return {
//       saveData,
//       loadSavedData,
//       clearSavedData,
//       isSaving,
//       isSaved,
//       isReady: isClient && isLocalStorageAvailable(),
//    };
// }
