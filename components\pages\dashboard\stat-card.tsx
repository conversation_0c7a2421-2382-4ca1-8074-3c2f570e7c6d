"use client";

import type React from "react";

import { Button } from "@/components/ui/button";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import { motion } from "framer-motion";
import { ArrowRightIcon } from "lucide-react";
import Link from "next/link";

interface StatCardProps {
   title: string;
   value: number;
   description?: string;
   icon: React.ReactNode;
   link: string;
   delay?: number;
}

export function StatCard({
   title,
   value,
   description,
   icon,
   link,
   delay = 0,
}: StatCardProps) {
   return (
      <motion.div
         initial={{ opacity: 0, y: 20 }}
         animate={{ opacity: 1, y: 0 }}
         transition={{ duration: 0.5, delay: delay * 0.1 }}
      >
         <Card className="overflow-hidden">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
               <CardTitle className="text-sm font-medium">{title}</CardTitle>
               <div className="h-5 w-5 text-muted-foreground">{icon}</div>
            </CardHeader>
            <CardContent>
               <div className="text-3xl font-semibold">{value}</div>
               {description && (
                  <CardDescription className="mt-2 text-xs">
                     {description}
                  </CardDescription>
               )}
               <div className="mt-4 flex justify-end">
                  <Button
                     variant="default"
                     size="sm"
                     className="gap-1 text-xs"
                     asChild
                  >
                     <Link href={link}>
                        View all
                        <ArrowRightIcon className="h-3 w-3" />
                     </Link>
                  </Button>
               </div>
            </CardContent>
         </Card>
      </motion.div>
   );
}
