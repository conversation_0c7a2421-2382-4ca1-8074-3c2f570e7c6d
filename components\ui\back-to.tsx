"use client";

import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { buttonVariants } from "./button";

type BackToProps = {
   link: string;
   text: string;
   variant?: "default" | "minimal" | "icon";
   className?: string;
};

function BackTo({ link, text, variant = "default", className }: BackToProps) {
   const variants = {
      default: (
         <Link
            href={link}
            className={cn(
               "group mb-4 flex items-center gap-1.5 text-sm font-medium text-muted-foreground transition-colors hover:text-foreground",
               className,
            )}
            aria-label={`Go back to ${text}`}
         >
            <ArrowLeft
               className="h-4 w-4 transition-transform duration-200 group-hover:-translate-x-0.5"
               aria-hidden="true"
            />
            <span>{text}</span>
         </Link>
      ),
      minimal: (
         <Link
            href={link}
            className={cn(
               buttonVariants({ variant: "ghost", size: "sm" }),
               "group gap-1.5 pl-2.5 pr-3 font-normal hover:bg-muted/50",
               className,
            )}
            aria-label={`Go back to ${text}`}
         >
            <ArrowLeft
               className="h-4 w-4 transition-transform duration-200 group-hover:-translate-x-0.5"
               aria-hidden="true"
            />
            <span>{text}</span>
         </Link>
      ),
      icon: (
         <Link
            href={link}
            className={cn(
               buttonVariants({ variant: "ghost", size: "icon" }),
               "h-8 w-8 rounded-full",
               className,
            )}
            aria-label={`Go back to ${text}`}
         >
            <ArrowLeft className="h-4 w-4" aria-hidden="true" />
         </Link>
      ),
   };

   return (
      <motion.div
         initial={{ opacity: 0, x: -10 }}
         animate={{ opacity: 1, x: 0 }}
         transition={{ duration: 0.2 }}
      >
         {variants[variant]}
      </motion.div>
   );
}

export default BackTo;
