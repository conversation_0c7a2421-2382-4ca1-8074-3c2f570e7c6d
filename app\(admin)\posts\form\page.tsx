import PostForm from "@/components/pages/posts/post-form";
import BackTo from "@/components/ui/back-to";
import Spinner from "@/components/ui/spinner";
import { getAuthors } from "@/lib/firebase/authors/service";
import { getCategories } from "@/lib/firebase/categories/service";
import { Suspense } from "react";

async function PostFormContent() {
   const categories = await getCategories();
   const authors = await getAuthors();

   return (
      <>
         <PostForm categories={categories} authors={authors} />
      </>
   );
}

export default function PostsFormPage() {
   return (
      <>
         <BackTo link="/posts" text="Back to posts" />
         <Suspense fallback={<Spinner />}>
            <PostFormContent />
         </Suspense>
      </>
   );
}
