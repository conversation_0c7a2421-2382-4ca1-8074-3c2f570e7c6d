import { Episode } from "@/lib/firebase/types";
import { createContext, useContext, useState } from "react";

type EpisodesContextValue = {
   episodes: Episode[];
   handleAddEpisode: (episode: Episode) => void;
   handleDeleteEpisode: (episodeNumber: number) => void;
   handleDeleteEpisodes: (episodes: Episode[]) => void;
   handleUpdateEpisode: (
      episodeNumber: number,
      updatedEpisode: Episode,
   ) => void;
   populateEpisodes: (episodes: Episode[]) => void;
};

const EpisodesContext = createContext<EpisodesContextValue | null>(null);

const EpisodesProvider = ({ children }: { children: React.ReactNode }) => {
   const [episodes, setEpisodes] = useState<Episode[]>([]);

   function handleAddEpisode(episode: Episode) {
      setEpisodes((prevEpisodes) => [...prevEpisodes, episode]);
   }

   const handleDeleteEpisode = (episodeNumber: number) => {
      setEpisodes((prevEpisodes) =>
         prevEpisodes.filter((e) => e.episodeNumber !== episodeNumber),
      );
   };

   const handleUpdateEpisode = (
      episodeNumber: number,
      updatedEpisode: Episode,
   ) => {
      setEpisodes((prevEpisodes) =>
         prevEpisodes.map((e) => {
            if (e.episodeNumber === episodeNumber) {
               return updatedEpisode;
            }
            return e;
         }),
      );
   };

   const handleDeleteEpisodes = (episodes: Episode[]) => {
      setEpisodes((prevEpisodes) =>
         prevEpisodes.filter((e) => !episodes.some((episode) => episode.episodeNumber === e.episodeNumber)),
      );
   };

   
   const populateEpisodes = (newEpisodes: Episode[]) => {
      setEpisodes(newEpisodes);
   };

   return (
      <EpisodesContext.Provider
         value={{
            episodes,
            handleAddEpisode,
            handleDeleteEpisode,
            handleDeleteEpisodes,
            handleUpdateEpisode,
            populateEpisodes
         }}
      >
         {children}
      </EpisodesContext.Provider>
   );
};

export const useEpisodes = () => {
   const context = useContext(EpisodesContext);
   if (!context) {
      throw new Error("useEpisodes must be used within a EpisodesProvider");
   }
   return context;
};

export default EpisodesProvider;
