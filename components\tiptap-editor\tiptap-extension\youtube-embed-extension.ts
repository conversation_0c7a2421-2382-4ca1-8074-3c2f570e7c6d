import { YouTubeEmbed } from "@/components/tiptap-editor/tiptap-node/youtube-embed-node/youtube-embed-node";
import { Node, mergeAttributes } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";

export interface YouTubeEmbedOptions {
   HTMLAttributes: Record<string, string | number | boolean>;
}

declare module "@tiptap/core" {
   interface Commands<ReturnType> {
      youtubeEmbed: {
         /**
          * Add a YouTube embed
          */
         setYouTubeEmbed: (options: { src: string }) => ReturnType;
      };
   }
}

export const YouTubeEmbedExtension = Node.create<YouTubeEmbedOptions>({
   name: "youtubeEmbed",

   group: "block",

   content: "",

   marks: "",

   atom: true,

   draggable: true,

   selectable: true,

   inline: false,

   addOptions() {
      return {
         HTMLAttributes: {
            class: "youtube-embed",
         },
      };
   },

   addAttributes() {
      return {
         src: {
            default: null,
         },
         width: {
            default: "100%",
         },
         height: {
            default: "315",
         },
         title: {
            default: "YouTube video player",
         },
      };
   },

   parseHTML() {
      return [
         {
            tag: 'div[data-type="youtube-embed"]',
            getAttrs: (node) => {
               const dom = node as HTMLElement;
               const iframe = dom.querySelector("iframe");
               if (iframe) {
                  const src = iframe.getAttribute("src");
                  const width = iframe.getAttribute("width") || "100%";
                  const height = iframe.getAttribute("height") || "315";
                  const title =
                     iframe.getAttribute("title") || "YouTube video player";
                  return { src, width, height, title };
               }
               return {};
            },
         },
         {
            tag: "iframe",
            getAttrs: (node) => {
               const dom = node as HTMLElement;
               const src = dom.getAttribute("src");
               if (
                  src &&
                  (src.includes("youtube.com/embed/") ||
                     src.includes("youtu.be/"))
               ) {
                  const width = dom.getAttribute("width") || "100%";
                  const height = dom.getAttribute("height") || "315";
                  const title =
                     dom.getAttribute("title") || "YouTube video player";
                  return { src, width, height, title };
               }
               return false;
            },
         },
      ];
   },

   renderHTML({ HTMLAttributes }) {
      const attrs = mergeAttributes(
         this.options.HTMLAttributes,
         HTMLAttributes,
      );

      // Helper function to extract YouTube video ID from URL
      const getYouTubeVideoId = (url: string | null) => {
         if (!url) return null;

         // Handle regular YouTube URLs
         const regExp =
            /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
         const match = url.match(regExp);

         // Handle YouTube Shorts
         const shortsRegExp = /^.*(youtube.com\/shorts\/)([^#&?]*).*/;
         const shortsMatch = url.match(shortsRegExp);

         return match && match[2].length === 11
            ? match[2]
            : shortsMatch && shortsMatch[2].length === 11
              ? shortsMatch[2]
              : null;
      };

      const videoId = getYouTubeVideoId(attrs.src);

      if (!videoId) {
         return [
            "div",
            mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
               "data-type": "youtube-embed",
            }),
         ];
      }

      const embedUrl = `https://www.youtube.com/embed/${videoId}`;

      return [
         "div",
         { class: "youtube-embed", "data-type": "youtube-embed" },
         [
            "iframe",
            {
               src: embedUrl,
               width: attrs.width || "100%",
               height: attrs.height || "315",
               style: "border: 0;",
               allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",
               allowfullscreen: "true",
               title: attrs.title || "YouTube video player",
            },
         ],
      ];
   },

   addNodeView() {
      return ReactNodeViewRenderer(YouTubeEmbed);
   },

   addCommands() {
      return {
         setYouTubeEmbed:
            (options) =>
            ({ commands }) => {
               return commands.insertContent({
                  type: this.name,
                  attrs: options,
               });
            },
      };
   },
});

export default YouTubeEmbedExtension;
