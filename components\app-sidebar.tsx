"use client";

import {
   <PERSON><PERSON>,
   <PERSON>bar<PERSON>ontent,
   Sidebar<PERSON>ooter,
   SidebarGroup,
   SidebarGroupContent,
   SidebarGroupLabel,
   SidebarHeader,
   SidebarMenu,
   SidebarMenuButton,
   SidebarMenuItem,
   useSidebar,
} from "@/components/ui/sidebar";

import LogoSmall from "@/public/logo-small.svg";
import Logo from "@/public/logo.svg";
import {
   ChartColumnStacked,
   Clapperboard,
   Film,
   LayoutDashboard,
   LogOut,
   NotebookText,
   User,
   UsersRound,
} from "lucide-react";
import { signOut, useSession } from "next-auth/react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React, { useEffect, useState, useTransition } from "react";
import { Button } from "./ui/button";

// Client-side only component for the sign-out button
function SignOutButton({
   state,
   isPending,
   handleSignOut,
}: {
   state: "expanded" | "collapsed";
   isPending: boolean;
   handleSignOut: () => void;
}) {
   // Use a state to track client-side hydration
   const [isClient, setIsClient] = useState(false);

   // After hydration, set isClient to true
   useEffect(() => {
      setIsClient(true);
   }, []);

   // During server-side rendering, always render the expanded button
   // to avoid hydration mismatches
   const isCollapsed = isClient && state === "collapsed";

   if (isCollapsed) {
      return (
         <Button
            size="icon"
            variant="ghost"
            onClick={handleSignOut}
            disabled={isPending}
            className="mx-auto flex h-10 w-10 items-center justify-center rounded-full"
         >
            {isPending ? (
               <svg className="h-5 w-5 animate-spin" viewBox="0 0 24 24">
                  <circle
                     className="opacity-25"
                     cx="12"
                     cy="12"
                     r="10"
                     stroke="currentColor"
                     strokeWidth="4"
                  />
                  <path
                     className="opacity-75"
                     fill="currentColor"
                     d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
               </svg>
            ) : (
               <LogOut size={20} />
            )}
         </Button>
      );
   }

   return (
      <Button
         variant="ghost"
         onClick={handleSignOut}
         disabled={isPending}
         className="w-full justify-start gap-2"
      >
         {isPending ? (
            <>
               <svg className="h-5 w-5 animate-spin" viewBox="0 0 24 24">
                  <circle
                     className="opacity-25"
                     cx="12"
                     cy="12"
                     r="10"
                     stroke="currentColor"
                     strokeWidth="4"
                  />
                  <path
                     className="opacity-75"
                     fill="currentColor"
                     d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
               </svg>
               <span>Signing out...</span>
            </>
         ) : (
            <>
               <LogOut size={18} />
               <span>Sign out</span>
            </>
         )}
      </Button>
   );
}

// Function to get navigation items based on user role
const getNavItems = (role?: string) => {
   return [
      {
         title: "Dashboard",
         url: "/dashboard",
         icon: LayoutDashboard,
      },
      ...(role === "admin"
         ? [
              {
                 title: "Users",
                 url: "/users",
                 icon: User,
              },
           ]
         : []),
      {
         title: "Posts",
         url: "/posts",
         icon: NotebookText,
      },
      {
         title: "Categories",
         url: "/categories",
         icon: ChartColumnStacked,
      },
      {
         title: "Authors",
         url: "/authors",
         icon: UsersRound,
      },
      {
         title: "Movies",
         url: "/movies",
         icon: Clapperboard,
      },
      {
         title: "Series",
         url: "/series",
         icon: Film,
      },
   ];
};

function SidebarLogo() {
   const { state, isMobile, setOpenMobile } = useSidebar();
   // Use a state to track client-side hydration
   const [isClient, setIsClient] = React.useState(false);

   // After hydration, set isClient to true
   React.useEffect(() => {
      setIsClient(true);
   }, []);

   const handleLogoClick = () => {
      if (isMobile) {
         setOpenMobile(false);
      }
   };

   // During server-side rendering or before hydration, always use the large logo
   // to avoid hydration mismatches
   const logoSrc = !isClient || state === "expanded" ? Logo : LogoSmall;
   const logoWidth = !isClient || state === "expanded" ? 100 : 20;
   const logoHeight = !isClient || state === "expanded" ? 35 : 20;

   return (
      <div className="flex w-full justify-center px-3 transition-all duration-200 ease-in-out group-data-[collapsible=icon]:px-0">
         <Link
            className="group/logo inline-flex"
            href="/"
            onClick={handleLogoClick}
         >
            <span className="sr-only">Logo</span>
            <Image
               src={logoSrc}
               alt="logo"
               width={logoWidth}
               height={logoHeight}
               priority
               className="transition-opacity duration-200 ease-in-out"
            />
         </Link>
      </div>
   );
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   const pathname = usePathname();
   const { data: session } = useSession();
   const { state, isMobile, setOpenMobile } = useSidebar();
   const [isPending, startTransition] = useTransition();

   // Function to check if a nav item should be active
   const isNavItemActive = (itemUrl: string) => {
      if (pathname === itemUrl) return true;

      if (itemUrl !== "/" && pathname.startsWith(`${itemUrl}/`)) return true;

      return false;
   };

   const handleClick = () => {
      if (isMobile) {
         setOpenMobile(false);
      }
   };

   const handleSignOut = () => {
      startTransition(async () => {
         await signOut({ callbackUrl: "/" });
      });
   };

   // Get navigation items based on user role
   const navItems = getNavItems(session?.user?.role);

   return (
      <Sidebar
         collapsible="icon"
         variant="sidebar"
         className="border-r border-border/40 shadow-sm"
         {...props}
      >
         <SidebarHeader className="mb-2 flex h-16 flex-row items-center justify-center max-md:mt-2">
            <SidebarLogo />
         </SidebarHeader>
         <SidebarContent className="px-2 group-data-[collapsible=icon]:px-0">
            <SidebarGroup>
               <SidebarGroupLabel className="px-2 py-1 text-xs font-medium uppercase text-muted-foreground/70">
                  General
               </SidebarGroupLabel>
               <SidebarGroupContent>
                  <SidebarMenu className="gap-2 space-y-1">
                     {navItems.map((item) => (
                        <SidebarMenuItem
                           key={item.title}
                           className="group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center"
                        >
                           <SidebarMenuButton
                              asChild
                              className="group/menu-button h-11 gap-3 rounded-md font-medium transition-colors group-data-[collapsible=icon]:px-0 [&>svg]:size-auto"
                              tooltip={item.title}
                              isActive={isNavItemActive(item.url)}
                           >
                              <Link
                                 href={item.url}
                                 className="flex items-center px-4 group-data-[collapsible=icon]:justify-center"
                                 onClick={handleClick}
                              >
                                 {item.icon && (
                                    <item.icon
                                       className="text-muted-foreground/70 group-data-[collapsible=icon]:mx-auto group-data-[active=true]/menu-button:text-primary"
                                       size={20}
                                       aria-hidden="true"
                                    />
                                 )}
                                 <span className="group-data-[collapsible=icon]:hidden">
                                    {item.title}
                                 </span>
                              </Link>
                           </SidebarMenuButton>
                        </SidebarMenuItem>
                     ))}
                  </SidebarMenu>
               </SidebarGroupContent>
            </SidebarGroup>
         </SidebarContent>
         <SidebarFooter className="px-2 pb-4">
            {session && (
               // Use a client-side only component for the sign-out button
               <SignOutButton
                  state={state}
                  isPending={isPending}
                  handleSignOut={handleSignOut}
               />
            )}
         </SidebarFooter>
      </Sidebar>
   );
}
