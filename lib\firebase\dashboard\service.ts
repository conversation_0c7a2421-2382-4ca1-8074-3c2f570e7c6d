import {
   collection,
   getDocs,
   limit,
   orderBy,
   query,
   where,
} from "firebase/firestore";
import { db } from "../firebase";

export type RecentContent = {
   id: string;
   title: string;
   date: Date;
   type: "Movie" | "Series" | "Post";
   image?: string;
   author?: string;
};

export type DashboardStats = {
   users: {
      total: number;
   };
   movies: {
      total: number;
   };
   series: {
      total: number;
   };
   categories: {
      total: number;
   };
   authors: {
      total: number;
   };
   admins: {
      total: number;
   };
   editors: {
      total: number;
   };
   posts: {
      total: number;
      recent: {
         title: string;
         id: string;
         date: Date;
      }[];
   };
   recentContent: RecentContent[];
};

export async function getDashboardStats(): Promise<DashboardStats> {
   // Fetch all collections
   const usersSnapshot = await getDocs(
      query(collection(db, "users"), where("role", "==", "user")),
   );
   const moviesSnapshot = await getDocs(collection(db, "movies"));
   const seriesSnapshot = await getDocs(collection(db, "series"));
   const categoriesSnapshot = await getDocs(collection(db, "categories"));
   const authorsSnapshot = await getDocs(collection(db, "authors"));
   const adminsSnapshot = await getDocs(
      query(collection(db, "users"), where("role", "==", "admin")),
   );
   const editorsSnapshot = await getDocs(
      query(collection(db, "users"), where("role", "==", "editor")),
   );
   const postsSnapshot = await getDocs(collection(db, "posts"));

   // Get recent content from movies, series, and posts
   const recentMovies = await getDocs(
      query(collection(db, "movies"), orderBy("createdAt", "desc"), limit(2)),
   );
   const recentSeries = await getDocs(
      query(collection(db, "series"), orderBy("createdAt", "desc"), limit(2)),
   );
   const recentPosts = await getDocs(
      query(collection(db, "posts"), orderBy("createdAt", "desc"), limit(2)),
   );

   // Combine recent content
   const recentContent: RecentContent[] = [
      ...recentMovies.docs.map((doc) => {
         const data = doc.data();
         return {
            id: doc.id,
            title: data.title,
            date: data.createdAt?.toDate() || new Date(),
            type: "Movie" as const,
            image: data.poster,
         };
      }),
      ...recentSeries.docs.map((doc) => {
         const data = doc.data();
         return {
            id: doc.id,
            title: data.title,
            date: data.createdAt?.toDate() || new Date(),
            type: "Series" as const,
            image: data.poster,
         };
      }),
      ...recentPosts.docs.map((doc) => {
         const data = doc.data();
         return {
            id: doc.id,
            title: data.title,
            date: data.createdAt?.toDate() || new Date(),
            type: "Post" as const,
            image: data.poster,
            author: data.author?.name,
         };
      }),
   ].sort((a, b) => b.date.getTime() - a.date.getTime());

   // Get recent posts
   const allRecentPost = postsSnapshot.docs
      .map((doc) => {
         const data = doc.data();
         return {
            id: doc.id,
            title: data.title,
            date: data.createdAt?.toDate() || new Date(),
         };
      })
      .sort((a, b) => b.date.getTime() - a.date.getTime())
      .slice(0, 6);

   return {
      users: {
         total: usersSnapshot.size,
      },
      movies: {
         total: moviesSnapshot.size,
      },
      series: {
         total: seriesSnapshot.size,
      },
      categories: {
         total: categoriesSnapshot.size,
      },
      authors: {
         total: authorsSnapshot.size,
      },
      admins: {
         total: adminsSnapshot.size,
      },
      editors: {
         total: editorsSnapshot.size,
      },
      posts: {
         total: postsSnapshot.size,
         recent: allRecentPost,
      },
      recentContent,
   };
}
