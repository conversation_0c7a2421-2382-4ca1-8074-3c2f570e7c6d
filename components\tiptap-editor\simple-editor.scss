@import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

body {
   --tt-toolbar-height: 44px;
   --tt-theme-text: var(--tt-gray-light-900);
   --tt-fullscreen-transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);

   .dark & {
      --tt-theme-text: var(--tt-gray-dark-900);
   }
}

.content-wrapper {
   height: calc(100% - var(--tt-toolbar-height));
   overflow-y: auto;
   background-color: var(--tt-bg-color);
   color: var(--tt-theme-text);
   max-height: 70vh;
   transition: var(--tt-fullscreen-transition);
   position: relative;

   &::-webkit-scrollbar {
      display: block;
      width: 0.5rem;
   }

   &::-webkit-scrollbar-track {
      background: transparent;
   }

   &::-webkit-scrollbar-thumb {
      background-color: var(--tt-scrollbar-color);
      border-radius: 4px;
   }

   & {
      /* Firefox scrollbar */
      scrollbar-width: thin;
      scrollbar-color: var(--tt-scrollbar-color) transparent;
   }
}

.simple-editor-content {
   // max-width: 640px;
   width: 100%;
   margin: 0 auto;
   position: relative;

   @media screen and (max-width: 480px) {
      margin-bottom: 3rem;
   }
}

.simple-editor-content .tiptap.ProseMirror {
   padding: 3rem 3rem;
   min-height: 70vh;
   transition: var(--tt-fullscreen-transition);
}

@media screen and (max-width: 480px) {
   .simple-editor-content .tiptap.ProseMirror {
      padding: 1rem 1.5rem;
   }
}

/* Fullscreen mode styles */
.simple-editor-container {
   position: relative;
   transition: var(--tt-fullscreen-transition);
}

.simple-editor-container.fullscreen {
   position: fixed;
   top: 0;
   left: 0;
   right: 0;
   bottom: 0;
   width: 100%;
   z-index: 100;
   background-color: var(--tt-bg-color);
   overflow: hidden;
   display: flex;
   flex-direction: column;

   .content-wrapper {
      max-height: none;
      flex: 1;
      overflow: hidden; /* Prevent scrolling in fullscreen mode */
   }

   .simple-editor-content .tiptap.ProseMirror {
      min-height: calc(100vh - var(--tt-toolbar-height));
      padding: 3rem 5rem;
      overflow-y: auto; /* Allow scrolling within the editor */
      max-height: calc(100vh - var(--tt-toolbar-height));
   }

   @media screen and (max-width: 768px) {
      .simple-editor-content .tiptap.ProseMirror {
         padding: 2rem 3rem;
      }
   }

   @media screen and (max-width: 480px) {
      .simple-editor-content .tiptap.ProseMirror {
         padding: 1.5rem 2rem;
      }
   }
}

/* Fullscreen button styles */
.fullscreen-button {
   position: absolute;
   top: 0.5rem;
   right: 0.5rem;
   z-index: 10;
   opacity: 0.7;
   transition: opacity 0.2s ease;

   &:hover {
      opacity: 1;
   }
}
