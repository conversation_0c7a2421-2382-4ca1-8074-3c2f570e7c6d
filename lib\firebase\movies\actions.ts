"use server";

import { MovieFormType } from "@/components/pages/movies/movie-form";
import { uploadImage } from "@/lib/cloudinary/cloudinary";
import { slugify } from "@/lib/utils";
import {
   collection,
   deleteDoc,
   doc,
   getDoc,
   getDocs,
   query,
   setDoc,
   Timestamp,
   updateDoc,
   where,
} from "firebase/firestore";
import { revalidatePath } from "next/cache";
import { db } from "../firebase";

export async function createMovie(data: MovieFormType) {
   const { title, poster, genres } = data;

   // Generate a slug from the movie title
   const slug = slugify(title);

   // Check if a movie with the same title already exists
   const moviesRef = collection(db, "movies");
   const q = query(moviesRef, where("title", "==", title));
   const querySnapshot = await getDocs(q);
   if (!querySnapshot.empty) {
      return {
         message: "A movie with the same title already exists",
         success: false,
      };
   }

   // Generate a unique ID for the new movie document
   const uniqueId = doc(collection(db, "movies")).id;

   // Upload image and get the URL
   const imageUrl =
      poster instanceof File && (await uploadImage(poster, uniqueId));
   if (!imageUrl) {
      return {
         message: "Image upload failed, please try again!",
         success: false,
      };
   }

   // Extract genre values
   const genreValues = genres.map((genre) => genre.value);

   // Create a new movie object
   const newMovie = {
      ...data,
      slug,
      poster: imageUrl,
      genres: genreValues,
      category: "movie",
      createdAt: Timestamp.now(),
   };

   const docRef = doc(db, "movies", uniqueId);

   try {
      // Save the new movie document to Firestore
      await setDoc(docRef, newMovie);

      revalidatePath("/movies");

      return {
         message: "Movie created successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to create movie",
         success: false,
      };
   }
}

export async function updateMovie(data: MovieFormType, id: string) {
   const { title, poster, genres } = data;

   // Generate a slug from the movie title
   const slug = slugify(title);

   // Check if a movie with the same title already exists
   const movieSnap = await getDoc(doc(db, "movies", id));
   if (!movieSnap.exists()) {
      return {
         message: "Movie not found",
         success: false,
      };
   }

   if (!(movieSnap.data().title === title)) {
      const moviesRef = collection(db, "movies");
      const q = query(moviesRef, where("title", "==", title));
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
         return {
            message: "A movie with the same title already exists",
            success: false,
         };
      }
   }

   // Upload image and get the URL
   const imageUrl =
      typeof poster === "string" ? poster : await uploadImage(poster, id);
   if (!imageUrl) {
      return {
         message: "Image upload failed, please try again!",
         success: false,
      };
   }

   // Extract genre values
   const genreValues = genres.map((genre) => genre.value);

   const docRef = doc(db, "movies", id);

   try {
      await updateDoc(docRef, {
         ...data,
         title,
         slug,
         poster: imageUrl,
         genres: genreValues,
      });

      revalidatePath("/movies");

      return {
         message: "Movie updated successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to update movie",
         success: false,
      };
   }
}

export async function deleteMovie(movieId: string) {
   if (!movieId) {
      return {
         message: "Movie ID is required",
         success: false,
      };
   }

   try {
      await deleteDoc(doc(db, "movies", movieId));
      revalidatePath("/movies");

      return {
         message: "Movie deleted successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to delete movie",
         success: false,
      };
   }
}
