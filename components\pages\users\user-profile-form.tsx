"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { Input } from "@/components/ui/input";
import { updateUserProfile } from "@/lib/firebase/users/actions";
import { getUserById } from "@/lib/firebase/users/service";
import { userProfileSchema } from "@/schemas/user-form-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { redirect, useSearchParams } from "next/navigation";
import { useEffect, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

function UserProfileForm() {
   const searchParams = useSearchParams();
   const id = searchParams.get("id");

   const [pending, startTransition] = useTransition();
   const [error, setError] = useState({ message: "" });
   const [userName, setUserName] = useState("");
   const [dataLoaded, setDataLoaded] = useState<boolean | undefined>(
      id ? false : undefined,
   );

   const form = useForm<z.infer<typeof userProfileSchema>>({
      resolver: zodResolver(userProfileSchema),
      defaultValues: {
         firstName: "",
         lastName: "",
         username: "",
         phone: "",
         location: "",
         occupation: "",
         bio: "",
         dateOfBirth: "",
         socialLinks: {
            instagram: "",
            facebook: "",
            youtube: "",
            tiktok: "",
            twitter: "",
         },
         portfolioLinks: {
            behance: "",
            dribbble: "",
            linkedin: "",
            website: "",
         },
      },
   });

   async function onSubmit(values: z.infer<typeof userProfileSchema>) {
      if (!id) {
         setError({ message: "User ID is required" });
         return;
      }

      startTransition(async () => {
         const result = await updateUserProfile(values, id);

         if (!result.success) {
            setError({ message: result.message });
            return;
         }

         toast.success(result.message);

         redirect("/users");
      });
   }

   // Load user data
   useEffect(() => {
      async function getUserData() {
         if (id) {
            try {
               const userData = await getUserById(id);
               if (userData) {
                  setUserName(userData.name || "");
                  form.reset({
                     firstName: userData.profile?.firstName || "",
                     lastName: userData.profile?.lastName || "",
                     username: userData.profile?.username || "",
                     phone: userData.profile?.phone || "",
                     location: userData.profile?.location || "",
                     occupation: userData.profile?.occupation || "",
                     bio: userData.profile?.bio || "",
                     dateOfBirth: userData.profile?.dateOfBirth || "",
                     socialLinks: userData.profile?.socialLinks || {
                        instagram: "",
                        facebook: "",
                        youtube: "",
                        tiktok: "",
                        twitter: "",
                     },
                     portfolioLinks: userData.profile?.portfolioLinks || {
                        behance: "",
                        dribbble: "",
                        linkedin: "",
                        website: "",
                     },
                  });
                  setDataLoaded(true);
               }
            } catch (error) {
               console.error("Error loading user:", error);
               toast.error("Error loading user data");
               setDataLoaded(false);
            }
         }
      }
      getUserData();
   }, [id, form]);

   if (!id) {
      return (
         <div className="rounded-md bg-destructive/15 p-4 text-destructive">
            User ID is required
         </div>
      );
   }

   return (
      <FormWrapper
         isSubmitting={pending}
         isDataLoaded={dataLoaded}
         loadingMessage="Loading profile form..."
         editingMessage="Loading user profile..."
         submittingMessage="Updating profile..."
      >
         <div className="space-y-6">
            <div className="space-y-2">
               <h1 className="text-3xl font-bold">Edit User Profile</h1>
               <p className="text-gray-500 dark:text-gray-400">
                  Update profile information for {userName}
               </p>
            </div>

            {error.message && (
               <div className="rounded-md bg-destructive/15 p-4 text-destructive">
                  {error.message}
               </div>
            )}

            <Form {...form}>
               <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
               >
                  <div className="grid gap-4 md:grid-cols-2">
                     <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>First Name</FormLabel>
                              <FormControl>
                                 <Input placeholder="First Name" {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />

                     <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>Last Name</FormLabel>
                              <FormControl>
                                 <Input placeholder="Last Name" {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                     <FormField
                        control={form.control}
                        name="username"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>Username</FormLabel>
                              <FormControl>
                                 <Input placeholder="Username" {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />

                     <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>Phone</FormLabel>
                              <FormControl>
                                 <Input placeholder="Phone Number" {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                     <FormField
                        control={form.control}
                        name="location"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>Location</FormLabel>
                              <FormControl>
                                 <Input placeholder="Location" {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />

                     <FormField
                        control={form.control}
                        name="occupation"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>Occupation</FormLabel>
                              <FormControl>
                                 <Input placeholder="Occupation" {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  <FormField
                     control={form.control}
                     name="bio"
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Bio</FormLabel>
                           <FormControl>
                              <Input placeholder="Short bio" {...field} />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />

                  <FormField
                     control={form.control}
                     name="dateOfBirth"
                     render={({ field }) => (
                        <FormItem>
                           <FormLabel>Date of Birth</FormLabel>
                           <FormControl>
                              <Input placeholder="YYYY-MM-DD" {...field} />
                           </FormControl>
                           <FormMessage />
                        </FormItem>
                     )}
                  />

                  {/* Social Links */}
                  <div className="space-y-4">
                     <h2 className="text-xl font-semibold">Social Links</h2>
                     <div className="grid gap-4 md:grid-cols-2">
                        <FormField
                           control={form.control}
                           name="socialLinks.instagram"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel>Instagram</FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="Instagram URL"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />

                        <FormField
                           control={form.control}
                           name="socialLinks.facebook"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel>Facebook</FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="Facebook URL"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                     </div>

                     <div className="grid gap-4 md:grid-cols-2">
                        <FormField
                           control={form.control}
                           name="socialLinks.twitter"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel>Twitter</FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="Twitter URL"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />

                        <FormField
                           control={form.control}
                           name="socialLinks.youtube"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel>YouTube</FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="YouTube URL"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                     </div>

                     <FormField
                        control={form.control}
                        name="socialLinks.tiktok"
                        render={({ field }) => (
                           <FormItem>
                              <FormLabel>TikTok</FormLabel>
                              <FormControl>
                                 <Input placeholder="TikTok URL" {...field} />
                              </FormControl>
                              <FormMessage />
                           </FormItem>
                        )}
                     />
                  </div>

                  {/* Portfolio Links */}
                  <div className="space-y-4">
                     <h2 className="text-xl font-semibold">Portfolio Links</h2>
                     <div className="grid gap-4 md:grid-cols-2">
                        <FormField
                           control={form.control}
                           name="portfolioLinks.behance"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel>Behance</FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="Behance URL"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />

                        <FormField
                           control={form.control}
                           name="portfolioLinks.dribbble"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel>Dribbble</FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="Dribbble URL"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                     </div>

                     <div className="grid gap-4 md:grid-cols-2">
                        <FormField
                           control={form.control}
                           name="portfolioLinks.linkedin"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel>LinkedIn</FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="LinkedIn URL"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />

                        <FormField
                           control={form.control}
                           name="portfolioLinks.website"
                           render={({ field }) => (
                              <FormItem>
                                 <FormLabel>Website</FormLabel>
                                 <FormControl>
                                    <Input
                                       placeholder="Website URL"
                                       {...field}
                                    />
                                 </FormControl>
                                 <FormMessage />
                              </FormItem>
                           )}
                        />
                     </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                     <Button
                        type="submit"
                        disabled={pending}
                        className="min-w-[120px]"
                     >
                        {pending ? (
                           <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Updating...
                           </>
                        ) : (
                           "Update Profile"
                        )}
                     </Button>
                  </div>
               </form>
            </Form>
         </div>
      </FormWrapper>
   );
}

export default UserProfileForm;
