import { auth } from "@/auth";
import UserForm from "@/components/pages/users/user-form";
import BackTo from "@/components/ui/back-to";
import Spinner from "@/components/ui/spinner";
import { redirect } from "next/navigation";
import { Suspense } from "react";

function UserFormContent() {
   return <UserForm />;
}

async function UserFormPageWrapper() {
   // Server-side check for user role
   const session = await auth();

   // If the user is an editor, redirect to dashboard
   if (session?.user?.role === "editor") {
      redirect("/dashboard");
   }

   return <UserFormContent />;
}

export default function UserFormPage() {
   return (
      <>
         <BackTo link="/users" text="Back to users" />
         <Suspense fallback={<Spinner />}>
            <UserFormPageWrapper />
         </Suspense>
      </>
   );
}
