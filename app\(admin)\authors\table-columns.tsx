"use client";

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import { Author, Post } from "@/lib/firebase/types";
import { ColumnDef } from "@tanstack/react-table";
import RowActions from "./row-actions";

export const columns: ColumnDef<Author>[] = [
   {
      id: "select",
      header: ({ table }) => (
         <Checkbox
            checked={
               table.getIsAllPageRowsSelected() ||
               (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
               table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
         />
      ),
      cell: ({ row }) => (
         <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
         />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
   },
   {
      header: "",
      accessorKey: "image",
      cell: ({ row }) => (
         <Avatar className="h-8 w-8">
            <AvatarImage
               src={row.getValue("image") || ""}
               alt={row.getValue("name")}
            />
            <AvatarFallback>
               {(row.getValue("name") as string)?.charAt(0)?.toUpperCase()}
            </AvatarFallback>
         </Avatar>
      ),
      size: 50,
      enableSorting: false,
   },
   {
      header: "Name",
      accessorKey: "name",
      cell: ({ row }) => (
         <div className="font-medium">{row.getValue("name")}</div>
      ),
      size: 180,
   },
   {
      header: "Posts",
      accessorKey: "posts",
      cell: ({ row }) => (
         <div className="font-medium">
            {(row.getValue("posts") as Post[]).length}
         </div>
      ),
      size: 180,
   },
   {
      header: "Bio",
      accessorKey: "bio",
      size: 600,
   },
   {
      id: "actions",
      header: () => <span className="sr-only">Actions</span>,
      cell: ({ row }) => <RowActions row={row} />,
      size: 60,
      enableHiding: false,
   },
];
