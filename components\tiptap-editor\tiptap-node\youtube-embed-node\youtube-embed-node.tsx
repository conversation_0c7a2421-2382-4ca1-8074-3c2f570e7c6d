"use client";

import { TrashIcon } from "@/components/tiptap-editor/tiptap-icons/trash-icon";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { NodeViewProps, NodeViewWrapper } from "@tiptap/react";
import * as React from "react";

export const YouTubeEmbed: React.FC<NodeViewProps> = (props) => {
   // Function to extract YouTube video ID from URL
   const getYouTubeVideoId = (url: string | null | undefined) => {
      if (!url) return null;

      // Handle regular YouTube URLs
      const regExp =
         /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
      const match = url.match(regExp);

      // Handle YouTube Shorts
      const shortsRegExp = /^.*(youtube.com\/shorts\/)([^#&?]*).*/;
      const shortsMatch = url.match(shortsRegExp);

      return match && match[2].length === 11
         ? match[2]
         : shortsMatch && shortsMatch[2].length === 11
           ? shortsMatch[2]
           : null;
   };

   // Check if we have a valid YouTube URL
   const hasValidSrc = React.useMemo(() => {
      return (
         !!props.node.attrs.src && !!getYouTubeVideoId(props.node.attrs.src)
      );
   }, [props.node.attrs.src]);

   const [isEditing, setIsEditing] = React.useState(!hasValidSrc);
   const [url, setUrl] = React.useState(props.node.attrs.src || "");
   const [width, setWidth] = React.useState(props.node.attrs.width || "100%");
   const [height, setHeight] = React.useState(props.node.attrs.height || "315");
   const [error, setError] = React.useState<string | null>(null);

   const videoId = getYouTubeVideoId(props.node.attrs.src);
   const embedUrl = videoId ? `https://www.youtube.com/embed/${videoId}` : "";

   const handleSave = (e: React.MouseEvent<HTMLButtonElement>) => {
      // Prevent default to avoid form submission
      e.preventDefault();
      e.stopPropagation();

      // Clear previous errors
      setError(null);

      const videoId = getYouTubeVideoId(url);
      if (!videoId) {
         setError("Please enter a valid YouTube URL");
         return;
      }

      props.updateAttributes({
         src: url,
         width,
         height,
      });
      setIsEditing(false);
   };

   const handleDelete = () => {
      props.deleteNode();
   };

   return (
      <NodeViewWrapper className="youtube-embed-node">
         {isEditing ? (
            <div className="youtube-embed-form space-y-4 rounded-md border bg-card p-4">
               <div className="flex flex-col gap-2">
                  <Label htmlFor="youtube-url">YouTube Video URL</Label>
                  <Input
                     id="youtube-url"
                     type="text"
                     placeholder="https://www.youtube.com/watch?v=..."
                     value={url}
                     onChange={(e) => {
                        setUrl(e.target.value);
                        // Clear error when user types
                        if (error) setError(null);
                     }}
                     className={error ? "border-red-500" : ""}
                  />
                  {error && <div className="text-sm text-red-500">{error}</div>}
               </div>

               <div className="flex gap-4">
                  <div className="flex flex-1 flex-col gap-2">
                     <Label htmlFor="youtube-width">Width</Label>
                     <Input
                        id="youtube-width"
                        type="text"
                        placeholder="100%"
                        value={width}
                        onChange={(e) => setWidth(e.target.value)}
                     />
                  </div>
                  <div className="flex flex-1 flex-col gap-2">
                     <Label htmlFor="youtube-height">Height</Label>
                     <Input
                        id="youtube-height"
                        type="text"
                        placeholder="315"
                        value={height}
                        onChange={(e) => setHeight(e.target.value)}
                     />
                  </div>
               </div>

               <div className="flex justify-end gap-2">
                  <Button
                     variant="outline"
                     type="button"
                     onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (!props.node.attrs.src) {
                           handleDelete();
                        } else {
                           setIsEditing(false);
                           setUrl(props.node.attrs.src);
                           setWidth(props.node.attrs.width);
                           setHeight(props.node.attrs.height);
                        }
                     }}
                  >
                     Cancel
                  </Button>
                  <Button onClick={handleSave} type="button">
                     Save
                  </Button>
               </div>
            </div>
         ) : (
            <div className="youtube-embed-container overflow-hidden rounded-md border">
               <div className="youtube-embed-toolbar flex justify-between bg-muted p-2">
                  <span className="text-sm font-medium">YouTube Video</span>
                  <div className="flex gap-2">
                     <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsEditing(true)}
                        className="h-8 px-2 text-xs"
                     >
                        Edit
                     </Button>
                     <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleDelete}
                        className="h-8 px-2 text-xs text-destructive"
                     >
                        <TrashIcon className="h-4 w-4" />
                     </Button>
                  </div>
               </div>
               <div className="youtube-embed-iframe-container">
                  {embedUrl ? (
                     <iframe
                        src={embedUrl}
                        width={props.node.attrs.width}
                        height={props.node.attrs.height}
                        style={{ border: 0 }}
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        allowFullScreen
                        title="YouTube video player"
                     ></iframe>
                  ) : (
                     <div className="youtube-embed-placeholder flex h-[315px] items-center justify-center bg-muted">
                        No YouTube URL provided
                     </div>
                  )}
               </div>
            </div>
         )}
      </NodeViewWrapper>
   );
};
