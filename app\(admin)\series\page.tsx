import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { DataTableWrapper } from "@/components/ui/data-table-wrapper";
import { getSeries } from "@/lib/firebase/series/service";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { columns } from "./table-columns";

// Separate component for the table content that will be wrapped with Suspense
async function SeriesTableContent() {
   const series = await getSeries();

   return (
      <DataTable
         data={series}
         columns={columns}
         defaultSortId="title"
         customButton={
            <Link href="/series/form">
               <Button className="ml-auto" variant="outline">
                  <PlusIcon
                     className="-ms-1 opacity-60"
                     size={16}
                     aria-hidden="true"
                  />
                  Add series
               </Button>
            </Link>
         }
      />
   );
}

async function SeriesPage() {
   return (
      <>
         <h1 className="pb-4 text-2xl font-semibold">Series Page</h1>
         <DataTableWrapper columnCount={8}>
            <SeriesTableContent />
         </DataTableWrapper>
      </>
   );
}

export default SeriesPage;
