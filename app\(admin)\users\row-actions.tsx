import { Button } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuGroup,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Profile, User } from "@/lib/firebase/types";
import { deleteUser, logoutUser } from "@/lib/firebase/users/actions";
import { Row } from "@tanstack/react-table";
import {
   EllipsisIcon,
   LogOut,
   TrashIcon,
   UserCog,
   UserRound,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

// Extended User type for the table that includes profile
type UserWithProfile = User & {
   profile?: Profile | null;
   emailVerified?: boolean;
   active?: boolean;
};

function RowActions({ row }: { row: Row<UserWithProfile> }) {
   async function handleDelete() {
      toast.promise(deleteUser(row.original.id), {
         loading: `Deleting ${row.original.name}...`,
         success: "User successfully deleted",
         error: "Error deleting user",
      });
   }

   async function handleLogout() {
      toast.promise(logoutUser(row.original.id), {
         loading: `Signing out ${row.original.name}...`,
         success: (data) => data.message || "User successfully signed out",
         error: "Error signing out user",
      });
   }

   return (
      <DropdownMenu>
         <DropdownMenuTrigger asChild>
            <div className="flex justify-end">
               <Button
                  size="icon"
                  variant="ghost"
                  className="shadow-none"
                  aria-label="Edit item"
               >
                  <EllipsisIcon size={16} aria-hidden="true" />
               </Button>
            </div>
         </DropdownMenuTrigger>
         <DropdownMenuContent align="end">
            <DropdownMenuGroup>
               <Link href={`/users/form?id=${row.original.id}`}>
                  <DropdownMenuItem>
                     <UserRound className="mr-1 h-4 w-4" />
                     <span>Edit User</span>
                  </DropdownMenuItem>
               </Link>
               <Link href={`/users/profile?id=${row.original.id}`}>
                  <DropdownMenuItem>
                     <UserCog className="mr-1 h-4 w-4" />
                     <span>Edit Profile</span>
                  </DropdownMenuItem>
               </Link>
               {row.original.active && (
                  <DropdownMenuItem onClick={handleLogout}>
                     <LogOut className="mr-1 h-4 w-4" />
                     <span>Sign Out</span>
                  </DropdownMenuItem>
               )}
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-destructive focus:text-destructive">
               <TrashIcon className="mr-1 h-4 w-4" />
               <span onClick={handleDelete}>Delete</span>
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
}

export default RowActions;
