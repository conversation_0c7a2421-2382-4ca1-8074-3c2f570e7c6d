"use client";

import * as AlertDialogPrimitive from "@radix-ui/react-alert-dialog";
import * as React from "react";

import { buttonVariants } from "@/components/ui/button";
import { cn } from "@/lib/utils";

function AlertDialog({
   ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {
   return <AlertDialogPrimitive.Root data-slot="alert-dialog" {...props} />;
}

function AlertDialogTrigger({
   ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {
   return (
      <AlertDialogPrimitive.Trigger
         data-slot="alert-dialog-trigger"
         {...props}
      />
   );
}

function AlertDialogPortal({
   ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {
   return (
      <AlertDialogPrimitive.Portal data-slot="alert-dialog-portal" {...props} />
   );
}

function AlertDialogOverlay({
   className,
   ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {
   return (
      <AlertDialogPrimitive.Overlay
         data-slot="alert-dialog-overlay"
         className={cn(
            "fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
            className,
         )}
         {...props}
      />
   );
}

AlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName;

function AlertDialogContent({
   className,
   ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {
   return (
      <AlertDialogPortal>
         <AlertDialogOverlay />
         <AlertDialogPrimitive.Content
            data-slot="alert-dialog-content"
            className={cn(
               "sm:max-w-100 fixed left-1/2 top-1/2 z-50 grid max-h-[calc(100%-2rem)] w-[480px] max-w-[calc(100%-2rem)] -translate-x-1/2 -translate-y-1/2 gap-4 overflow-y-auto rounded-xl border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
               className,
            )}
            {...props}
         />
      </AlertDialogPortal>
   );
}

function AlertDialogHeader({
   className,
   ...props
}: React.ComponentProps<"div">) {
   return (
      <div
         data-slot="alert-dialog-header"
         className={cn(
            "flex flex-col gap-1 text-center sm:text-left",
            className,
         )}
         {...props}
      />
   );
}

function AlertDialogFooter({
   className,
   ...props
}: React.ComponentProps<"div">) {
   return (
      <div
         data-slot="alert-dialog-footer"
         className={cn(
            "flex flex-col-reverse gap-3 sm:flex-row sm:justify-end",
            className,
         )}
         {...props}
      />
   );
}

function AlertDialogTitle({
   className,
   ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {
   return (
      <AlertDialogPrimitive.Title
         data-slot="alert-dialog-title"
         className={cn("text-lg font-semibold", className)}
         {...props}
      />
   );
}

function AlertDialogDescription({
   className,
   ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {
   return (
      <AlertDialogPrimitive.Description
         data-slot="alert-dialog-description"
         className={cn("text-sm text-muted-foreground", className)}
         {...props}
      />
   );
}

function AlertDialogAction({
   className,
   ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {
   return (
      <AlertDialogPrimitive.Action
         className={cn(buttonVariants(), className)}
         {...props}
      />
   );
}

function AlertDialogCancel({
   className,
   ...props
}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {
   return (
      <AlertDialogPrimitive.Cancel
         className={cn(buttonVariants({ variant: "outline" }), className)}
         {...props}
      />
   );
}

export {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogOverlay,
   AlertDialogPortal,
   AlertDialogTitle,
   AlertDialogTrigger,
};
