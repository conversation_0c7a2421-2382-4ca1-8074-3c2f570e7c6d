import { auth } from "@/auth";
import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { DataTableWrapper } from "@/components/ui/data-table-wrapper";
import { getUsers } from "@/lib/firebase/users/service";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { redirect } from "next/navigation";
import { columns } from "./table-columns";

// Separate component for the table content that will be wrapped with Suspense
async function UsersTableContent() {
   const users = await getUsers();

   return (
      <DataTable
         data={users}
         columns={columns}
         defaultSortId="name"
         filterBy="name"
         customButton={
            <Link href="/users/form">
               <Button className="ml-auto" variant="outline">
                  <PlusIcon
                     className="-ms-1 opacity-60"
                     size={16}
                     aria-hidden="true"
                  />
                  Add user
               </Button>
            </Link>
         }
      />
   );
}

async function UsersPage() {
   // Server-side check for user role
   const session = await auth();

   // If the user is an editor, redirect to dashboard
   if (session?.user?.role === "editor") {
      redirect("/dashboard");
   }

   return (
      <>
         <h1 className="pb-4 text-2xl font-semibold">Users Management</h1>
         <DataTableWrapper columnCount={10}>
            <UsersTableContent />
         </DataTableWrapper>
      </>
   );
}

export default UsersPage;
