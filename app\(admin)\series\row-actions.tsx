import { Button } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuGroup,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { deleteSeries } from "@/lib/firebase/series/actions";
import { Series } from "@/lib/firebase/types";
import { Row } from "@tanstack/react-table";
import { EllipsisIcon, TrashIcon } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";

export function RowActions({ row }: { row: Row<Series> }) {
   async function handleDelete() {
      toast.promise(deleteSeries(row.original.id), {
         loading: `Deleting ${row.original.title} series...`,
         success: "Series successfully deleted",
         error: "Error deleting series",
      });
   }

   return (
      <DropdownMenu>
         <DropdownMenuTrigger asChild>
            <div className="flex justify-end">
               <Button
                  size="icon"
                  variant="ghost"
                  className="shadow-none"
                  aria-label="Edit item"
               >
                  <EllipsisIcon size={16} aria-hidden="true" />
               </Button>
            </div>
         </DropdownMenuTrigger>
         <DropdownMenuContent align="end">
            <DropdownMenuGroup>
               <Link href={`/series/form?id=${row.original.id}`}>
                  <DropdownMenuItem>
                     <span>Edit</span>
                  </DropdownMenuItem>
               </Link>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem
               className="text-destructive focus:text-destructive"
               onClick={handleDelete}
            >
               <TrashIcon className="mr-1 h-4 w-4" />
               <span>Delete</span>
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
}
