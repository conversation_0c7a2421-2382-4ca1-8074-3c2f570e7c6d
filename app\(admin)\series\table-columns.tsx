"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Checkbox } from "@/components/ui/checkbox";
import { Episode, Series } from "@/lib/firebase/types";
import { ColumnDef, FilterFn } from "@tanstack/react-table";
import { RowActions } from "./row-actions";

// Custom filter function for multi-column searching
const multiColumnFilterFn: FilterFn<Series> = (row, columnId, filterValue) => {
   const searchableRowContent =
      `${row.original.title} ${row.original.year}`.toLowerCase();
   const searchTerm = (filterValue ?? "").toLowerCase();
   return searchableRowContent.includes(searchTerm);
};

export const columns: ColumnDef<Series>[] = [
   {
      id: "select",
      header: ({ table }) => (
         <Checkbox
            checked={
               table.getIsAllPageRowsSelected() ||
               (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
               table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
         />
      ),
      cell: ({ row }) => (
         <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
         />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
   },
   {
      header: "",
      accessorKey: "poster",
      cell: ({ row }) => (
         <Avatar className="h-8 w-8">
            <AvatarImage
               src={row.getValue("poster") || ""}
               alt={row.getValue("title")}
            />
            <AvatarFallback>
               {(row.getValue("title") as string)?.charAt(0)?.toUpperCase()}
            </AvatarFallback>
         </Avatar>
      ),
      size: 50,
      enableSorting: false,
   },
   {
      header: "Title",
      accessorKey: "title",
      cell: ({ row }) => (
         <div className="font-medium">{row.getValue("title")}</div>
      ),
      size: 200,
      filterFn: multiColumnFilterFn,
      enableHiding: false,
   },
   {
      header: "Description",
      accessorKey: "description",
      cell: ({ row }) => (
         <div className="line-clamp-1">{row.getValue("description")}</div>
      ),
      size: 420,
   },
   {
      header: "Year",
      accessorKey: "year",
      size: 80,
   },
   {
      header: "Category",
      accessorKey: "category",
      size: 80,
   },
   {
      header: "Episodes",
      accessorKey: "episodes",
      cell: ({ row }) => (
         <div className="line-clamp-1">
            {(row.getValue("episodes") as Episode[]).length}
         </div>
      ),
      size: 80,
   },
   {
      id: "actions",
      header: () => <span className="sr-only">Actions</span>,
      cell: ({ row }) => <RowActions row={row} />,
      size: 60,
      enableHiding: false,
   },
];
