import {
   collection,
   doc,
   getDoc,
   getDocs,
   orderBy,
   query,
   where,
} from "firebase/firestore";
import { db } from "../firebase";
import { Profile, User } from "../types";

export async function getUsers() {
   const usersRef = collection(db, "users");
   const q = query(usersRef, orderBy("name"));
   const querySnapshot = await getDocs(q);

   const users = await Promise.all(
      querySnapshot.docs.map(async (userDoc) => {
         const userData = userDoc.data() as User;
         const userId = userDoc.id;

         // Get the profile data for this user
         const profileRef = doc(db, "profiles", userId);
         const profileSnap = await getDoc(profileRef);
         const profileData = profileSnap.exists()
            ? ({
                 ...profileSnap.data(),
                 createdAt: profileSnap.data()?.createdAt?.toDate(),
                 updatedAt: profileSnap.data()?.updatedAt?.toDate(),
              } as Profile)
            : null;

         // Check if user has active sessions
         const sessionsQuery = query(
            collection(db, "sessions"),
            where("userId", "==", userId),
         );
         const sessionsSnapshot = await getDocs(sessionsQuery);
         const isActive = sessionsSnapshot.size > 0;

         return {
            ...userData,
            id: userId,
            profile: profileData,
            active: isActive,
         };
      }),
   );

   return users;
}

export async function getUserById(id: string) {
   if (!id) return null;

   // Get user data
   const userRef = doc(db, "users", id);
   const userSnap = await getDoc(userRef);

   if (!userSnap.exists()) {
      return null;
   }

   const userData = userSnap.data() as User;

   // Get profile data
   const profileRef = doc(db, "profiles", id);
   const profileSnap = await getDoc(profileRef);
   const profileData = profileSnap.exists()
      ? ({
           ...profileSnap.data(),
           createdAt: profileSnap.data()?.createdAt?.toDate(),
           updatedAt: profileSnap.data()?.updatedAt?.toDate(),
        } as Profile)
      : null;

   // Check if user has active sessions
   const sessionsQuery = query(
      collection(db, "sessions"),
      where("userId", "==", id),
   );
   const sessionsSnapshot = await getDocs(sessionsQuery);
   const isActive = sessionsSnapshot.size > 0;

   return {
      ...userData,
      id: id,
      profile: profileData,
      active: isActive,
   };
}

export async function getProfileById(id: string) {
   if (!id) return null;

   const profileRef = doc(db, "profiles", id);
   const profileSnap = await getDoc(profileRef);

   if (!profileSnap.exists()) {
      return null;
   }

   return {
      id: profileSnap.id,
      ...profileSnap.data(),
      createdAt: profileSnap.data()?.createdAt?.toDate(),
      updatedAt: profileSnap.data()?.updatedAt?.toDate(),
   } as Profile;
}
