"use client";

import {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
   AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuCheckboxItem,
   DropdownMenuContent,
   DropdownMenuLabel,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
   Pagination,
   PaginationContent,
   PaginationItem,
} from "@/components/ui/pagination";
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from "@/components/ui/select";
import {
   Table,
   TableBody,
   TableCell,
   TableHead,
   TableHeader,
   TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import {
   ColumnDef,
   ColumnFiltersState,
   PaginationState,
   SortingState,
   VisibilityState,
   flexRender,
   getCoreRowModel,
   getFacetedUniqueValues,
   getFilteredRowModel,
   getPaginationRowModel,
   getSortedRowModel,
   useReactTable,
} from "@tanstack/react-table";
import {
   ChevronDownIcon,
   ChevronFirstIcon,
   ChevronLastIcon,
   ChevronLeftIcon,
   ChevronRightIcon,
   ChevronUpIcon,
   CircleAlertIcon,
   CircleXIcon,
   Columns3Icon,
   ListFilterIcon,
   TrashIcon,
} from "lucide-react";
import { useId, useRef, useState } from "react";

interface DataTableProps<TData, TValue> {
   columns: ColumnDef<TData, TValue>[];
   data: TData[];
   defaultSortId: string;
   filterBy?: string;
   customButton?: React.ReactNode;
   showFilter?: boolean;
   toggleColumnVisibility?: boolean;
   showPagination?: boolean;
   deleteAction?: (data: TData[]) => void;
}

export function DataTable<TData, TValue>({
   columns,
   data,
   defaultSortId,
   filterBy,
   customButton,
   showFilter = true,
   toggleColumnVisibility = true,
   showPagination = true,
   deleteAction,
}: DataTableProps<TData, TValue>) {
   const id = useId();
   const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
   const [columnVisibility, setColumnVisibility] = useState<VisibilityState>(
      {},
   );
   const [pagination, setPagination] = useState<PaginationState>({
      pageIndex: 0,
      pageSize: 10,
   });
   const inputRef = useRef<HTMLInputElement>(null);

   const [sorting, setSorting] = useState<SortingState>([
      {
         id: defaultSortId,
         desc: false,
      },
   ]);

   const table = useReactTable({
      data,
      columns,
      getCoreRowModel: getCoreRowModel(),
      getSortedRowModel: getSortedRowModel(),
      onSortingChange: setSorting,
      enableSortingRemoval: false,
      getPaginationRowModel: getPaginationRowModel(),
      onPaginationChange: setPagination,
      onColumnFiltersChange: setColumnFilters,
      onColumnVisibilityChange: setColumnVisibility,
      getFilteredRowModel: getFilteredRowModel(),
      getFacetedUniqueValues: getFacetedUniqueValues(),
      state: {
         sorting,
         pagination,
         columnFilters,
         columnVisibility,
      },
   });

   return (
      <div className="max-w-full space-y-4">
         {/* Filters */}

         <div className="flex flex-wrap items-center justify-between gap-3">
            <div className="flex items-center gap-3">
               {/* Filter by title */}
               {showFilter && (
                  <div className="relative">
                     <Input
                        id={`${id}-input`}
                        ref={inputRef}
                        className={cn(
                           "peer min-w-60 ps-9",
                           Boolean(
                              table
                                 .getColumn(filterBy ?? "title")
                                 ?.getFilterValue(),
                           ) && "pe-9",
                        )}
                        value={
                           (table
                              .getColumn(filterBy ?? "title")
                              ?.getFilterValue() ?? "") as string
                        }
                        onChange={(e) =>
                           table
                              .getColumn(filterBy ?? "title")
                              ?.setFilterValue(e.target.value)
                        }
                        placeholder={`Filter by ${filterBy ?? "title"}`}
                        type="text"
                        aria-label={`Filter by ${filterBy ?? "title"}`}
                     />
                     <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
                        <ListFilterIcon size={16} aria-hidden="true" />
                     </div>
                     {Boolean(
                        table.getColumn(filterBy ?? "title")?.getFilterValue(),
                     ) && (
                        <button
                           className="absolute inset-y-0 end-0 flex h-full w-9 items-center justify-center rounded-e-lg text-muted-foreground/80 outline-offset-2 outline-ring/30 transition-colors hover:text-foreground focus:z-10 focus-visible:outline-2 disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 dark:outline-ring/40"
                           aria-label="Clear filter"
                           onClick={() => {
                              table
                                 .getColumn(filterBy ?? "title")
                                 ?.setFilterValue("");
                              if (inputRef.current) {
                                 inputRef.current.focus();
                              }
                           }}
                        >
                           <CircleXIcon size={16} aria-hidden="true" />
                        </button>
                     )}
                  </div>
               )}
               {/* Toggle columns visibility */}
               {toggleColumnVisibility && (
                  <DropdownMenu>
                     <DropdownMenuTrigger asChild>
                        <Button variant="outline">
                           <Columns3Icon
                              className="-ms-1 opacity-60"
                              size={16}
                              aria-hidden="true"
                           />
                           View
                        </Button>
                     </DropdownMenuTrigger>
                     <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
                        {table
                           .getAllColumns()
                           .filter((column) => column.getCanHide())
                           .map((column) => {
                              return (
                                 <DropdownMenuCheckboxItem
                                    key={column.id}
                                    className="capitalize"
                                    checked={column.getIsVisible()}
                                    onCheckedChange={(value) =>
                                       column.toggleVisibility(!!value)
                                    }
                                    onSelect={(event) => event.preventDefault()}
                                 >
                                    {column.id}
                                 </DropdownMenuCheckboxItem>
                              );
                           })}
                     </DropdownMenuContent>
                  </DropdownMenu>
               )}
            </div>
            <div className="flex items-center gap-3">
               {/* Delete button */}
               {table.getSelectedRowModel().rows.length > 0 && (
                  <AlertDialog>
                     <AlertDialogTrigger asChild>
                        <Button className="ml-auto" variant="outline">
                           <TrashIcon
                              className="-ms-1 opacity-60"
                              size={16}
                              aria-hidden="true"
                           />
                           Delete
                           <span className="-me-1 inline-flex h-5 max-h-full items-center rounded border bg-background px-1 font-[inherit] text-[0.625rem] font-medium text-muted-foreground/70">
                              {table.getSelectedRowModel().rows.length}
                           </span>
                        </Button>
                     </AlertDialogTrigger>
                     <AlertDialogContent>
                        <div className="flex flex-col gap-2 max-sm:items-center sm:flex-row sm:gap-4">
                           <div
                              className="flex size-9 shrink-0 items-center justify-center rounded-full border"
                              aria-hidden="true"
                           >
                              <CircleAlertIcon
                                 className="opacity-80"
                                 size={16}
                              />
                           </div>
                           <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                 This action cannot be undone. This will
                                 permanently delete{" "}
                                 {table.getSelectedRowModel().rows.length}{" "}
                                 selected{" "}
                                 {table.getSelectedRowModel().rows.length === 1
                                    ? "row"
                                    : "rows"}
                                 .
                              </AlertDialogDescription>
                           </AlertDialogHeader>
                        </div>
                        <AlertDialogFooter>
                           <AlertDialogCancel>Cancel</AlertDialogCancel>
                           <AlertDialogAction
                              onClick={() =>
                                 typeof deleteAction === "function"
                                    ? deleteAction(
                                         table
                                            .getSortedRowModel()
                                            .rows.map((row) => row.original),
                                      )
                                    : alert("Function not yet implemented")
                              }
                           >
                              Delete
                           </AlertDialogAction>
                        </AlertDialogFooter>
                     </AlertDialogContent>
                  </AlertDialog>
               )}
               {customButton && <>{customButton}</>}
            </div>
         </div>

         {/* Table */}
         <div className="overflow-x-auto overflow-y-hidden rounded-lg border bg-background">
            <Table className="max-w-full table-fixed">
               <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                     <TableRow
                        key={headerGroup.id}
                        className="hover:bg-transparent"
                     >
                        {headerGroup.headers.map((header) => {
                           return (
                              <TableHead
                                 key={header.id}
                                 style={{ width: `${header.getSize()}px` }}
                                 className="h-11"
                              >
                                 {header.isPlaceholder ? null : header.column.getCanSort() ? (
                                    <div
                                       className={cn(
                                          header.column.getCanSort() &&
                                             "flex h-full cursor-pointer select-none items-center justify-between gap-2",
                                       )}
                                       onClick={header.column.getToggleSortingHandler()}
                                       onKeyDown={(e) => {
                                          // Enhanced keyboard handling for sorting
                                          if (
                                             header.column.getCanSort() &&
                                             (e.key === "Enter" ||
                                                e.key === " ")
                                          ) {
                                             e.preventDefault();
                                             header.column.getToggleSortingHandler()?.(
                                                e,
                                             );
                                          }
                                       }}
                                       tabIndex={
                                          header.column.getCanSort()
                                             ? 0
                                             : undefined
                                       }
                                    >
                                       {flexRender(
                                          header.column.columnDef.header,
                                          header.getContext(),
                                       )}
                                       {{
                                          asc: (
                                             <ChevronUpIcon
                                                className="shrink-0 opacity-60"
                                                size={16}
                                                aria-hidden="true"
                                             />
                                          ),
                                          desc: (
                                             <ChevronDownIcon
                                                className="shrink-0 opacity-60"
                                                size={16}
                                                aria-hidden="true"
                                             />
                                          ),
                                       }[
                                          header.column.getIsSorted() as string
                                       ] ?? null}
                                    </div>
                                 ) : (
                                    flexRender(
                                       header.column.columnDef.header,
                                       header.getContext(),
                                    )
                                 )}
                              </TableHead>
                           );
                        })}
                     </TableRow>
                  ))}
               </TableHeader>

               <TableBody>
                  {table.getRowModel().rows?.length ? (
                     table.getRowModel().rows.map((row) => (
                        <TableRow
                           key={row.id}
                           data-state={row.getIsSelected() && "selected"}
                        >
                           {row.getVisibleCells().map((cell) => (
                              <TableCell key={cell.id} className="last:py-0">
                                 {flexRender(
                                    cell.column.columnDef.cell,
                                    cell.getContext(),
                                 )}
                              </TableCell>
                           ))}
                        </TableRow>
                     ))
                  ) : (
                     <TableRow>
                        <TableCell
                           colSpan={columns.length}
                           className="h-24 text-center"
                        >
                           No results.
                        </TableCell>
                     </TableRow>
                  )}
               </TableBody>
            </Table>
         </div>

         {/* Pagination */}
         {showPagination && (
            <div className="flex items-center justify-between gap-8">
               {/* Results per page */}
               <div className="flex items-center gap-3">
                  <Label htmlFor={id} className="max-sm:sr-only">
                     Rows per page
                  </Label>
                  <Select
                     value={table.getState().pagination.pageSize.toString()}
                     onValueChange={(value) => {
                        table.setPageSize(Number(value));
                     }}
                  >
                     <SelectTrigger id={id} className="w-fit whitespace-nowrap">
                        <SelectValue placeholder="Select number of results" />
                     </SelectTrigger>
                     <SelectContent className="[&_*[role=option]>span]:end-2 [&_*[role=option]>span]:start-auto [&_*[role=option]]:pe-8 [&_*[role=option]]:ps-2">
                        {[5, 10, 25, 50].map((pageSize) => (
                           <SelectItem
                              key={pageSize}
                              value={pageSize.toString()}
                           >
                              {pageSize}
                           </SelectItem>
                        ))}
                     </SelectContent>
                  </Select>
               </div>
               {/* Page number information */}
               <div className="flex grow justify-end whitespace-nowrap text-sm text-muted-foreground">
                  <p
                     className="whitespace-nowrap text-sm text-muted-foreground"
                     aria-live="polite"
                  >
                     <span className="text-foreground">
                        {table.getState().pagination.pageIndex *
                           table.getState().pagination.pageSize +
                           1}
                        -
                        {Math.min(
                           Math.max(
                              table.getState().pagination.pageIndex *
                                 table.getState().pagination.pageSize +
                                 table.getState().pagination.pageSize,
                              0,
                           ),
                           table.getRowCount(),
                        )}
                     </span>{" "}
                     of{" "}
                     <span className="text-foreground">
                        {table.getRowCount().toString()}
                     </span>
                  </p>
               </div>

               {/* Pagination buttons */}
               <div>
                  <Pagination>
                     <PaginationContent>
                        {/* First page button */}
                        <PaginationItem>
                           <Button
                              size="icon"
                              variant="outline"
                              className="disabled:pointer-events-none disabled:opacity-50"
                              onClick={() => table.firstPage()}
                              disabled={!table.getCanPreviousPage()}
                              aria-label="Go to first page"
                           >
                              <ChevronFirstIcon size={16} aria-hidden="true" />
                           </Button>
                        </PaginationItem>
                        {/* Previous page button */}
                        <PaginationItem>
                           <Button
                              size="icon"
                              variant="outline"
                              className="disabled:pointer-events-none disabled:opacity-50"
                              onClick={() => table.previousPage()}
                              disabled={!table.getCanPreviousPage()}
                              aria-label="Go to previous page"
                           >
                              <ChevronLeftIcon size={16} aria-hidden="true" />
                           </Button>
                        </PaginationItem>
                        {/* Next page button */}
                        <PaginationItem>
                           <Button
                              size="icon"
                              variant="outline"
                              className="disabled:pointer-events-none disabled:opacity-50"
                              onClick={() => table.nextPage()}
                              disabled={!table.getCanNextPage()}
                              aria-label="Go to next page"
                           >
                              <ChevronRightIcon size={16} aria-hidden="true" />
                           </Button>
                        </PaginationItem>
                        {/* Last page button */}
                        <PaginationItem>
                           <Button
                              size="icon"
                              variant="outline"
                              className="disabled:pointer-events-none disabled:opacity-50"
                              onClick={() => table.lastPage()}
                              disabled={!table.getCanNextPage()}
                              aria-label="Go to last page"
                           >
                              <ChevronLastIcon size={16} aria-hidden="true" />
                           </Button>
                        </PaginationItem>
                     </PaginationContent>
                  </Pagination>
               </div>
            </div>
         )}
      </div>
   );
}
