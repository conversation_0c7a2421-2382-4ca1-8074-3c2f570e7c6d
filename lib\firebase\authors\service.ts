import { collection, doc, getDoc, getDocs } from "firebase/firestore";
import { db } from "../firebase";
import { getPostsByAuthorId } from "../posts/service";
import { Author } from "../types";

export async function getAuthors() {
   const querySnapshot = await getDocs(collection(db, "authors"));

   const authors = await Promise.all(
      querySnapshot.docs.map(async (doc) => {
         return {
            id: doc.id,
            ...doc.data(),
            posts: await getPostsByAuthorId(doc.id),
         } as Author;
      }),
   );

   return authors;
}

export async function getAuthorById(id: string) {
   if (!id) return null;

   const docRef = doc(db, "authors", id);

   const docSnap = await getDoc(docRef);

   if (!docSnap.exists()) {
      throw new Error(`Author with id ${id} not found`);
   }

   const author = {
      id: docSnap.id,
      ...docSnap.data(),
   } as Author;

   return author;
}
