import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/ui/data-table";
import { DataTableWrapper } from "@/components/ui/data-table-wrapper";
import { getMovies } from "@/lib/firebase/movies/service";
import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { columns } from "./table-columns";

// Separate component for the table content that will be wrapped with Suspense
async function MoviesTableContent() {
   const movies = await getMovies();

   return (
      <DataTable
         data={movies}
         columns={columns}
         defaultSortId="title"
         customButton={
            <Link href="/movies/form">
               <Button className="ml-auto" variant="outline">
                  <PlusIcon
                     className="-ms-1 opacity-60"
                     size={16}
                     aria-hidden="true"
                  />
                  Add movie
               </Button>
            </Link>
         }
      />
   );
}

async function MoviesPage() {
   return (
      <>
         <h1 className="pb-4 text-2xl font-semibold">Movies Page</h1>
         <DataTableWrapper columnCount={7}>
            <MoviesTableContent />
         </DataTableWrapper>
      </>
   );
}

export default MoviesPage;
