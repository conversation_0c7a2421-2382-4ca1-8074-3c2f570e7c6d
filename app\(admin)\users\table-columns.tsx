"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Profile, User } from "@/lib/firebase/types";
import { ColumnDef, Row } from "@tanstack/react-table";
import RowActions from "./row-actions";

// Extended User type for the table that includes profile
type UserWithProfile = User & {
   profile?: Profile | null;
   emailVerified?: boolean;
   active?: boolean;
};

// Helper function for multi-column filtering
export function multiColumnFilterFn(
   row: Row<UserWithProfile>,
   columnId: string,
   filterValue: string,
) {
   const searchableRowContent =
      `${row.original.name} ${row.original.email} ${row.original.role} ${row.original.profile?.phone}`.toLowerCase();
   const searchTerm = (filterValue ?? "").toLowerCase();
   return searchableRowContent.includes(searchTerm);
}

export const columns: ColumnDef<UserWithProfile>[] = [
   {
      id: "select",
      header: ({ table }) => (
         <Checkbox
            checked={
               table.getIsAllPageRowsSelected() ||
               (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
               table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
         />
      ),
      cell: ({ row }) => (
         <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
         />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
   },
   {
      header: "",
      accessorKey: "image",
      cell: ({ row }) => (
         <Avatar className="h-8 w-8">
            <AvatarImage
               src={row.getValue("image") || ""}
               alt={row.getValue("name")}
            />
            <AvatarFallback>
               {(row.getValue("name") as string)?.charAt(0)?.toUpperCase()}
            </AvatarFallback>
         </Avatar>
      ),
      size: 50,
      enableSorting: false,
   },
   {
      header: "Name",
      accessorKey: "name",
      cell: ({ row }) => (
         <div className="font-medium">{row.getValue("name")}</div>
      ),
      size: 180,
      filterFn: multiColumnFilterFn,
   },
   {
      header: "Email",
      accessorKey: "email",
      size: 220,
      filterFn: multiColumnFilterFn,
   },
   {
      header: "Username",
      accessorKey: "profile.username",
      cell: ({ row }) => <div>{row.original.profile?.username || "-"}</div>,
      size: 150,
   },
   {
      header: "Phone",
      accessorKey: "profile.phone",
      cell: ({ row }) => <div>{row.original.profile?.phone || "-"}</div>,
      size: 150,
   },
   {
      header: "Role",
      accessorKey: "role",
      cell: ({ row }) => {
         const role = row.getValue("role") as string;
         return (
            <Badge
               variant={
                  role === "admin"
                     ? "default"
                     : role === "editor"
                       ? "outline"
                       : "secondary"
               }
            >
               {role}
            </Badge>
         );
      },
      size: 100,
   },
   {
      header: "Verified",
      accessorKey: "emailVerified",
      cell: ({ row }) => (
         <div>{row.getValue("emailVerified") ? "Yes" : "No"}</div>
      ),
      size: 100,
   },
   {
      header: "Active",
      accessorKey: "active",
      cell: ({ row }) => {
         const isActive = row.getValue("active") as boolean;
         return (
            <div
               className={
                  isActive
                     ? "font-medium text-green-600"
                     : "text-muted-foreground"
               }
            >
               {isActive ? "Online" : "Offline"}
            </div>
         );
      },
      size: 100,
   },
   {
      id: "actions",
      header: () => <span className="sr-only">Actions</span>,
      cell: ({ row }) => <RowActions row={row} />,
      size: 60,
      enableHiding: false,
   },
];
