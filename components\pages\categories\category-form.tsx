"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Form,
   FormControl,
   FormField,
   FormItem,
   FormLabel,
   FormMessage,
} from "@/components/ui/form";
import { FormWrapper } from "@/components/ui/form-wrapper";
import { Input } from "@/components/ui/input";
import { ACCEPTED_IMAGE_TYPES, MAX_FILE_SIZE } from "@/lib/constants";
import {
   createCategory,
   updateCategory,
} from "@/lib/firebase/categories/actions";
import { getCategoryById } from "@/lib/firebase/categories/service";
import { formatBytes } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

const formSchema = z.object({
   name: z.string().min(3, {
      message: "Name must be at least 3 characters.",
   }),
   description: z.string().min(3, {
      message: "Description must be at least 3 characters.",
   }),
   image: z.union([
      z
         .instanceof(File, {
            message: "Please select an image file.",
         })
         .refine((file) => file.size <= MAX_FILE_SIZE, {
            message: `The image is too large. Please choose an image smaller than ${formatBytes(MAX_FILE_SIZE)}.`,
         })
         .refine((file) => ACCEPTED_IMAGE_TYPES.includes(file.type), {
            message: "Please upload a valid image file (JPEG, PNG, or WebP).",
         }),
      z.string().url({
         message: "Please provide a valid URL for the image.",
      }),
   ]),
});

export type CategoryFormType = z.infer<typeof formSchema>;

function CategoryForm() {
   const searchParams = useSearchParams();
   const router = useRouter();
   const id = searchParams.get("id");

   const [pending, startTransition] = useTransition();
   const [error, setError] = useState({ message: "" });
   const [image, setImage] = useState({
      prevImage: "",
      newImage: "" as string | Blob,
   });
   const [dataLoaded, setDataLoaded] = useState<boolean | undefined>(
      id ? false : undefined,
   );

   const form = useForm<z.infer<typeof formSchema>>({
      resolver: zodResolver(formSchema),
      defaultValues: {
         name: "",
         description: "",
         image: undefined,
      },
   });

   async function onSubmit(values: z.infer<typeof formSchema>) {
      startTransition(async () => {
         const result = id
            ? await updateCategory(values, id)
            : await createCategory(values);

         if (!result.success) {
            setError(result);
            toast.error(result.message);
            return;
         }

         toast.success(result.message);
         form.reset();
         router.push("/categories");
      });
   }

   useEffect(() => {
      async function getCategoryData() {
         if (!id) return;

         const data = await getCategoryById(id);

         if (data) {
            form.setValue("name", data.name);
            form.setValue("description", data.description);
            form.setValue("image", data.image);
            setImage((prev) => ({ ...prev, prevImage: data.image }));
            setDataLoaded(true);
         } else {
            setDataLoaded(false);
         }
      }

      getCategoryData();
   }, [id, form]);

   return (
      <FormWrapper isSubmitting={pending} isDataLoaded={dataLoaded}>
         <h1 className="pb-4 text-2xl font-semibold">
            {id ? "Update" : "Add New"} Category
         </h1>

         <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
               <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Category Name</FormLabel>
                        <FormControl>
                           <Input placeholder="Category Name" {...field} />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Category Description</FormLabel>
                        <FormControl>
                           <Input
                              placeholder="Category Description"
                              {...field}
                           />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               <FormField
                  control={form.control}
                  name="image"
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Category Image</FormLabel>
                        <FormControl>
                           <Input
                              type="file"
                              accept="image/*"
                              placeholder="Category Image"
                              onChange={(e) => {
                                 const file = e.target.files?.[0];
                                 if (file) {
                                    field.onChange(file);
                                    setImage((prev) => ({
                                       ...prev,
                                       newImage: file,
                                    }));
                                 }
                              }}
                           />
                        </FormControl>
                        <FormMessage />
                     </FormItem>
                  )}
               />

               <div className="flex gap-2">
                  {image.prevImage && (
                     <div className="relative aspect-square h-20 overflow-hidden rounded-xl">
                        <Image
                           className="object-cover"
                           src={image.prevImage}
                           alt={""}
                           fill
                        />
                     </div>
                  )}
                  {image.newImage && (
                     <div className="relative aspect-square h-20 overflow-hidden rounded-xl">
                        <Image
                           className="object-cover"
                           src={URL.createObjectURL(image.newImage as Blob)}
                           alt=""
                           fill
                        />
                     </div>
                  )}
               </div>

               {error.message && (
                  <p className="text-sm font-medium text-destructive">
                     {error.message}
                  </p>
               )}
               <Button type="submit" disabled={pending}>
                  {pending && <Loader2 className="animate-spin" />}
                  {pending ? "" : id ? "Update" : "Add"}
               </Button>
            </form>
         </Form>
      </FormWrapper>
   );
}

export default CategoryForm;
