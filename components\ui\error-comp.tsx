import { Button } from "./button";

type Props = {
   error: Error;
   reset: () => void;
};

function ErrorComp({ error, reset }: Props) {
   return (
      <main className="flex min-h-96 w-full flex-col items-center justify-center">
         <h1 className="text-4xl">Something went wrong!</h1>
         <p className="mb-4">{error.message}</p>
         <Button onClick={reset}>Try Again</Button>
      </main>
   );
}

export default ErrorComp;
