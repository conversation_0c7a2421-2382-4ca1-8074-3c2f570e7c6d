"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import {
   Card,
   CardContent,
   CardDescription,
   CardHeader,
   CardTitle,
} from "@/components/ui/card";
import type { RecentContent as RecentContentType } from "@/lib/firebase/dashboard/service";
import { format } from "date-fns";
import { motion } from "framer-motion";
import Link from "next/link";

interface RecentContentProps {
   items: RecentContentType[];
}

export function RecentContent({ items }: RecentContentProps) {
   return (
      <motion.div
         initial={{ opacity: 0, y: 20 }}
         animate={{ opacity: 1, y: 0 }}
         transition={{ duration: 0.5, delay: 0.4 }}
      >
         <Card className="h-full">
            <CardHeader>
               <CardTitle>Recent Content</CardTitle>
               <CardDescription>
                  Latest content added to the platform
               </CardDescription>
            </CardHeader>
            <CardContent>
               <div className="space-y-4">
                  {items.map((item) => (
                     <Link
                        key={item.id}
                        href={
                           item.type === "Movie"
                              ? `/movies/form?id=${item.id}`
                              : item.type === "Series"
                                ? `/series/form?id=${item.id}`
                                : `/posts/form?id=${item.id}`
                        }
                        className="group"
                     >
                        <div className="flex items-center gap-3 rounded-lg p-2 transition-colors hover:bg-muted/50">
                           <Avatar className="h-12 w-12 rounded-lg">
                              {item.image ? (
                                 <AvatarImage
                                    src={item.image}
                                    alt={item.title}
                                    className="object-cover"
                                 />
                              ) : (
                                 <AvatarFallback className="rounded-md">
                                    {item.title.substring(0, 2)}
                                 </AvatarFallback>
                              )}
                           </Avatar>
                           <div className="flex-1 space-y-1">
                              <p className="font-medium leading-none">
                                 {item.title}
                              </p>
                              <div className="flex items-center text-xs text-muted-foreground">
                                 {item.type === "Post" && item.author && (
                                    <span className="mr-2">
                                       By {item.author}
                                    </span>
                                 )}
                                 <span>
                                    {isValidDate(item.date)
                                       ? format(item.date, "dd/MM/yyyy")
                                       : "Invalid Date"}
                                 </span>
                              </div>
                           </div>
                           <Badge variant="outline">{item.type}</Badge>
                        </div>
                     </Link>
                  ))}
               </div>
            </CardContent>
         </Card>
      </motion.div>
   );
}

function isValidDate(date: unknown): boolean {
   return date instanceof Date && !isNaN(date.getTime());
}
