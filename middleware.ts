import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
   // Check if the request is for a user management page or its sub-paths
   if (request.nextUrl.pathname.startsWith("/users")) {
      // Look for the authjs.session-token cookie
      const sessionCookie = request.cookies.get("authjs.session-token");

      // Try to get session data from the session cookie
      try {
         const response = await fetch(
            `${request.nextUrl.origin}/api/auth/session`,
            {
               headers: {
                  cookie: `${sessionCookie?.name}=${sessionCookie?.value}`,
               },
            },
         );

         if (response.ok) {
            const session = await response.json();

            if (session?.user?.role === "editor") {
               console.log("Fallback: Redirecting editor using session API");
               return NextResponse.redirect(new URL("/dashboard", request.url));
            }
         }
      } catch (e) {
         console.error("Error checking session:", e);
      }
   }

   return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
   matcher: ["/users", "/users/:path*"],
};
