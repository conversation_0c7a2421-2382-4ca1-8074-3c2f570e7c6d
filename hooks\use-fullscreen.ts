"use client";

import { useCallback, useEffect, useState } from "react";

/**
 * A hook to manage fullscreen state with keyboard shortcuts
 * @param options Configuration options
 * @returns Fullscreen state and toggle function
 */
export function useFullscreen({
   onEnter,
   onExit,
}: {
   onEnter?: () => void;
   onExit?: () => void;
} = {}) {
   const [isFullscreen, setIsFullscreen] = useState(false);

   const toggleFullscreen = useCallback(() => {
      setIsFullscreen((prev) => {
         const newState = !prev;
         if (newState && onEnter) {
            onEnter();
         } else if (!newState && onExit) {
            onExit();
         }
         return newState;
      });
   }, [onEnter, onExit]);

   const exitFullscreen = useCallback(() => {
      if (isFullscreen) {
         setIsFullscreen(false);
         if (onExit) onExit();
      }
   }, [isFullscreen, onExit]);

   // Prevent body scrolling when dialog is open
   useEffect(() => {
      if (isFullscreen) {
         document.body.style.overflow = "hidden";
      }

      return () => {
         document.body.style.overflow = "";
      };
   }, [isFullscreen]);

   // Handle keyboard shortcuts
   useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
         // F11 key or Ctrl+Shift+F
         if (
            e.key === "F11" ||
            (e.ctrlKey && e.shiftKey && e.key.toLowerCase() === "f")
         ) {
            e.preventDefault();
            toggleFullscreen();
         }

         // Escape key to exit fullscreen
         if (e.key === "Escape" && isFullscreen) {
            e.preventDefault();
            exitFullscreen();
         }
      };

      document.addEventListener("keydown", handleKeyDown);
      return () => {
         document.removeEventListener("keydown", handleKeyDown);
      };
   }, [isFullscreen, toggleFullscreen, exitFullscreen]);

   return {
      isFullscreen,
      toggleFullscreen,
      exitFullscreen,
   };
}

export default useFullscreen;
