import { But<PERSON> } from "@/components/ui/button";
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Episode } from "@/lib/firebase/types";
import { Row } from "@tanstack/react-table";
import { EllipsisIcon, TrashIcon } from "lucide-react";
import { useEpisodes } from "./context/episodesProvider";
import EpisodeDialog from "./episode-dialog";

export function EpisodeAction({ row }: { row: Row<Episode> }) {
   const { handleDeleteEpisode } = useEpisodes();

   return (
      <DropdownMenu>
         <DropdownMenuTrigger asChild>
            <div className="flex justify-end">
               <Button
                  size="icon"
                  variant="ghost"
                  className="shadow-none"
                  aria-label="Edit item"
               >
                  <EllipsisIcon size={16} aria-hidden="true" />
               </Button>
            </div>
         </DropdownMenuTrigger>
         <DropdownMenuContent align="end">
            <EpisodeDialog title="Edit Episode" episode={row.original}>
               <DropdownMenuItem
                  onSelect={(e) => e.preventDefault()}
                  className="cursor-pointer"
               >
                  <span>Edit</span>
               </DropdownMenuItem>
            </EpisodeDialog>
            <DropdownMenuSeparator />
            <DropdownMenuItem
               className="cursor-pointer text-destructive focus:text-destructive"
               onClick={() => handleDeleteEpisode(row.original.episodeNumber)}
            >
               <TrashIcon className="mr-1 h-4 w-4" />
               <span>Delete</span>
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
}
