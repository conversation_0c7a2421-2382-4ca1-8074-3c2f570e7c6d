import * as React from "react";

import { cn } from "@/lib/utils";

function Table({ className, ...props }: React.ComponentProps<"table">) {
   return (
      <div className="relative w-full overflow-x-auto">
         <table
            data-slot="table"
            className={cn(
               "w-full min-w-full caption-bottom text-sm",
               className,
            )}
            {...props}
         />
      </div>
   );
}

function TableHeader({ className, ...props }: React.ComponentProps<"thead">) {
   return (
      <thead data-slot="table-header" className={cn(className)} {...props} />
   );
}

function TableBody({ className, ...props }: React.ComponentProps<"tbody">) {
   return (
      <tbody
         data-slot="table-body"
         className={cn("[&_tr:last-child]:border-0", className)}
         {...props}
      />
   );
}

function TableFooter({ className, ...props }: React.ComponentProps<"tfoot">) {
   return (
      <tfoot
         data-slot="table-footer"
         className={cn(
            "border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",
            className,
         )}
         {...props}
      />
   );
}

function TableRow({ className, ...props }: React.ComponentProps<"tr">) {
   return (
      <tr
         data-slot="table-row"
         className={cn(
            "border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",
            className,
         )}
         {...props}
      />
   );
}

function TableHead({ className, ...props }: React.ComponentProps<"th">) {
   return (
      <th
         data-slot="table-head"
         className={cn(
            "h-12 px-3 text-left align-middle font-medium text-muted-foreground has-[role=checkbox]:w-px [&:has([role=checkbox])]:pr-0",
            className,
         )}
         {...props}
      />
   );
}

function TableCell({ className, ...props }: React.ComponentProps<"td">) {
   return (
      <td
         data-slot="table-cell"
         className={cn(
            "truncate break-words p-3 align-middle [&:has([role=checkbox])]:pr-0",
            className,
         )}
         {...props}
      />
   );
}

function TableCaption({
   className,
   ...props
}: React.ComponentProps<"caption">) {
   return (
      <caption
         data-slot="table-caption"
         className={cn("mt-4 text-sm text-muted-foreground", className)}
         {...props}
      />
   );
}

export {
   Table,
   TableBody,
   TableCaption,
   TableCell,
   TableFooter,
   TableHead,
   TableHeader,
   TableRow,
};
