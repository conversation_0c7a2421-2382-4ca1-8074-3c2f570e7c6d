import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
   return twMerge(clsx(inputs));
}

export const formatBytes = (bytes: number, decimals = 2) => {
   if (bytes === 0) return "0 Bytes";
   const k = 1024;
   const dm = decimals < 0 ? 0 : decimals;
   const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
   const i = Math.floor(Math.log(bytes) / Math.log(k));
   return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
};

export function getSlugFromName(name: string): string {
   return name.trim().toLowerCase().replace(/\s+/g, "-").slice(0, 100);
}

export function slugify(text: string): string {
   if (!text) {
      return "";
   }

   return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // Remove all non-word characters except dashes and spaces
      .replace(/[\s_-]+/g, "-") // Replace spaces or multiple dashes with a single dash
      .replace(/^-+|-+$/g, ""); // Remove leading/trailing dashes
}