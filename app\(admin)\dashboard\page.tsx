import { auth } from "@/auth";
import { RecentContent } from "@/components/pages/dashboard/recent-content";
import { RecentPosts } from "@/components/pages/dashboard/recent-posts";
import { StatCard } from "@/components/pages/dashboard/stat-card";
import Spinner from "@/components/ui/spinner";
import { getDashboardStats } from "@/lib/firebase/dashboard/service";
import {
   BookText,
   Clapperboard,
   Film,
   LayoutGrid,
   PenTool,
   Shield,
   Users,
} from "lucide-react";
import { Suspense } from "react";

async function DashboardContent() {
   const stats = await getDashboardStats();
   const session = await auth();
   const isEditor = session?.user?.role === "editor";

   // Define which stat cards to show based on user role
   const getStatCards = () => {
      // Base stat cards that all roles can see
      const baseCards = [
         <StatCard
            key="movies"
            title="Movies"
            value={stats.movies.total}
            icon={<Film className="h-5 w-5" />}
            link="/movies"
            delay={0}
         />,
         <StatCard
            key="series"
            title="Series"
            value={stats.series.total}
            icon={<Clapperboard className="h-5 w-5" />}
            link="/series"
            delay={1}
         />,
         <StatCard
            key="categories"
            title="Categories"
            value={stats.categories.total}
            icon={<LayoutGrid className="h-5 w-5" />}
            link="/categories"
            delay={2}
         />,
         <StatCard
            key="authors"
            title="Authors"
            value={stats.authors.total}
            icon={<BookText className="h-5 w-5" />}
            link="/authors"
            delay={3}
         />,
         <StatCard
            key="posts"
            title="Posts"
            value={stats.posts.total}
            icon={<BookText className="h-5 w-5" />}
            link="/posts"
            delay={4}
         />,
      ];

      // Add admin-only stat cards if the user is not an editor
      if (!isEditor) {
         return [
            <StatCard
               key="users"
               title="Total Users"
               value={stats.users.total}
               icon={<Users className="h-5 w-5" />}
               link="/users"
               delay={0}
            />,
            ...baseCards,
            <StatCard
               key="admins"
               title="Admins"
               value={stats.admins.total}
               icon={<Shield className="h-5 w-5" />}
               link="/admins"
               delay={5}
            />,
            <StatCard
               key="editors"
               title="Editors"
               value={stats.editors.total}
               icon={<PenTool className="h-5 w-5" />}
               link="/users"
               delay={6}
            />,
         ];
      }

      return baseCards;
   };

   return (
      <div className="space-y-6">
         <div className="flex flex-col gap-4">
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground">
               Welcome to PimPim admin dashboard. Here&apos;s an overview of the
               platform.
            </p>
         </div>

         <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {getStatCards()}
         </div>

         <div className="grid gap-4 md:grid-cols-2">
            <RecentContent items={stats.recentContent} />
            <RecentPosts posts={stats.posts.recent} />
         </div>
      </div>
   );
}

export default function DashboardPage() {
   return (
      <Suspense fallback={<Spinner />}>
         <DashboardContent />
      </Suspense>
   );
}
