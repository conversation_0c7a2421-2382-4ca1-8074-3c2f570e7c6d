import { InstagramEmbed } from "@/components/tiptap-editor/tiptap-node/instagram-embed-node/instagram-embed-node";
import { Node, mergeAttributes } from "@tiptap/core";
import { ReactNodeViewRenderer } from "@tiptap/react";

export interface InstagramEmbedOptions {
   // HTMLAttributes: Record<string, any>;
   HTMLAttributes: Record<string, string | number | boolean>;
}

declare module "@tiptap/core" {
   interface Commands<ReturnType> {
      instagramEmbed: {
         /**
          * Add an Instagram embed
          */
         setInstagramEmbed: (options: { src: string }) => ReturnType;
      };
   }
}

export const InstagramEmbedExtension = Node.create<InstagramEmbedOptions>({
   name: "instagramEmbed",

   group: "block",

   content: "",

   marks: "",

   atom: true,

   draggable: true,

   selectable: true,

   inline: false,

   addOptions() {
      return {
         HTMLAttributes: {
            class: "instagram-embed",
         },
      };
   },

   addAttributes() {
      return {
         src: {
            default: null,
         },
         width: {
            default: "100%",
         },
         captioned: {
            default: false,
         },
         title: {
            default: "Instagram post",
         },
      };
   },

   parseHTML() {
      return [
         {
            tag: 'div[data-type="instagram-embed"]',
            getAttrs: (node) => {
               const dom = node as HTMLElement;
               const blockquote = dom.querySelector(
                  "blockquote.instagram-media",
               );
               if (blockquote) {
                  const permalink = blockquote.getAttribute(
                     "data-instgrm-permalink",
                  );
                  if (permalink) {
                     const captioned = blockquote.hasAttribute(
                        "data-instgrm-captioned",
                     );
                     const width = dom.style.width || "100%";
                     return { src: permalink, width, captioned };
                  }
               }
               return {};
            },
         },
         {
            tag: "blockquote.instagram-media",
            getAttrs: (node) => {
               const dom = node as HTMLElement;
               const permalink = dom.getAttribute("data-instgrm-permalink");
               if (permalink) {
                  const captioned = dom.hasAttribute("data-instgrm-captioned");
                  return { src: permalink, captioned };
               }
               return false;
            },
         },
      ];
   },

   renderHTML({ HTMLAttributes }) {
      const attrs = mergeAttributes(
         this.options.HTMLAttributes,
         HTMLAttributes,
      );

      // Helper function to extract Instagram post ID from URL
      const getInstagramPostId = (url: string | null) => {
         if (!url) return null;

         const regExp = /instagram.com\/p\/([^/?#&]+)/;
         const match = url.match(regExp);
         return match ? match[1] : null;
      };

      const postId = getInstagramPostId(attrs.src);

      if (!postId) {
         return [
            "div",
            mergeAttributes(this.options.HTMLAttributes, HTMLAttributes, {
               "data-type": "instagram-embed",
            }),
         ];
      }

      return [
         "div",
         { class: "instagram-embed", "data-type": "instagram-embed" },
         [
            "blockquote",
            {
               class: "instagram-media",
               "data-instgrm-captioned": attrs.captioned ? "" : undefined,
               "data-instgrm-permalink": `https://www.instagram.com/p/${postId}/`,
               "data-instgrm-version": "14",
               style: "background: #FFF; border: 0; border-radius: 3px; box-shadow: 0 0 1px 0 rgba(0,0,0,0.5), 0 1px 10px 0 rgba(0,0,0,0.15); margin: 1px; max-width: 540px; min-width: 326px; padding: 0; width: 99.375%;",
            },
         ],
         ["script", { async: "true", src: "//www.instagram.com/embed.js" }],
      ];
   },

   addNodeView() {
      return ReactNodeViewRenderer(InstagramEmbed);
   },

   addCommands() {
      return {
         setInstagramEmbed:
            (options) =>
            ({ commands }) => {
               return commands.insertContent({
                  type: this.name,
                  attrs: options,
               });
            },
      };
   },
});

export default InstagramEmbedExtension;
