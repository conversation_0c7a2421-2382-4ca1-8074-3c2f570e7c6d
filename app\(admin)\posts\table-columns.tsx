"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { POST_SECTIONS } from "@/lib/constants/post-sections";
import { Author, Category, Post } from "@/lib/firebase/types";
import { ColumnDef, FilterFn } from "@tanstack/react-table";
import { RowActions } from "./row-actions";

// Custom filter function for multi-column searching
const multiColumnFilterFn: FilterFn<Post> = (row, columnId, filterValue) => {
   const searchableRowContent =
      `${row.original.title} ${row.original.author.name}`.toLowerCase();
   const searchTerm = (filterValue ?? "").toLowerCase();
   return searchableRowContent.includes(searchTerm);
};

export const columns: ColumnDef<Post>[] = [
   {
      id: "select",
      header: ({ table }) => (
         <Checkbox
            checked={
               table.getIsAllPageRowsSelected() ||
               (table.getIsSomePageRowsSelected() && "indeterminate")
            }
            onCheckedChange={(value) =>
               table.toggleAllPageRowsSelected(!!value)
            }
            aria-label="Select all"
         />
      ),
      cell: ({ row }) => (
         <Checkbox
            checked={row.getIsSelected()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
            aria-label="Select row"
         />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
   },
   {
      header: "",
      accessorKey: "poster",
      cell: ({ row }) => (
         <Avatar className="h-8 w-8">
            <AvatarImage
               src={row.getValue("poster") || ""}
               alt={row.getValue("title")}
            />
            <AvatarFallback>
               {(row.getValue("title") as string)?.charAt(0)?.toUpperCase()}
            </AvatarFallback>
         </Avatar>
      ),
      size: 50,
      enableSorting: false,
   },
   {
      header: "Title",
      accessorKey: "title",
      cell: ({ row }) => (
         <div className="line-clamp-1 font-medium">{row.getValue("title")}</div>
      ),
      size: 300,
      filterFn: multiColumnFilterFn,
      enableHiding: false,
   },
   {
      header: "Summary",
      accessorKey: "summary",
      cell: ({ row }) => (
         <div className="line-clamp-1">{row.getValue("summary")}</div>
      ),
      size: 420,
   },
   {
      header: "Author",
      accessorKey: "author",
      cell: ({ row }) => (
         <div className="line-clamp-1">
            {(row.getValue("author") as Author)?.name}
         </div>
      ),
      size: 80,
   },
   {
      header: "Category",
      accessorKey: "category",
      cell: ({ row }) => (
         <div className="line-clamp-1">
            {(row.getValue("category") as Category)?.name}
         </div>
      ),
      size: 80,
   },
   {
      header: "Sections",
      accessorKey: "sections",
      cell: ({ row }) => {
         const sections = row.original.sections || [];
         return (
            <div className="flex flex-wrap gap-1">
               {sections.map((sectionId) => {
                  const section = POST_SECTIONS.find((s) => s.id === sectionId);
                  return section ? (
                     <Badge
                        key={sectionId}
                        variant="secondary"
                        className="text-xs"
                     >
                        {section.name}
                     </Badge>
                  ) : null;
               })}
               {sections.length === 0 && (
                  <span className="text-xs text-muted-foreground">None</span>
               )}
            </div>
         );
      },
      size: 150,
   },
   {
      id: "actions",
      header: () => <span className="sr-only">Actions</span>,
      cell: ({ row }) => <RowActions row={row} />,
      size: 60,
      enableHiding: false,
   },
];
