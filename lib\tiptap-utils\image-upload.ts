"use client";

import { ACCEPTED_IMAGE_TYPES } from "@/lib/constants";
import { uploadPostImage } from "@/lib/firebase/posts/actions";
import { MAX_FILE_SIZE } from "@/lib/tiptap-utils";
import { formatBytes } from "@/lib/utils";

/**
 * <PERSON>les image upload to Cloudinary with progress tracking and abort capability
 */
export const handleCloudinaryImageUpload = async (
   file: File,
   onProgress?: (event: { progress: number }) => void,
   abortSignal?: AbortSignal,
): Promise<string> => {
   try {
      // Check if the upload has been aborted
      if (abortSignal?.aborted) {
         throw new Error("Upload cancelled");
      }

      // Check file size
      if (file.size > MAX_FILE_SIZE) {
         const errorMessage = `File is too large. Maximum size is ${formatBytes(MAX_FILE_SIZE)}.`;
         throw new Error(errorMessage);
      }

      // Check file type
      if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
         const errorMessage =
            "File type not supported. Please upload a JPEG, PNG, or WebP image.";
         throw new Error(errorMessage);
      }

      // Show initial progress
      onProgress?.({ progress: 10 });

      // Show progress before upload starts
      onProgress?.({ progress: 30 });

      // Upload the image to Cloudinary
      const imageUrl = await uploadPostImage(file);

      // Show completion progress
      onProgress?.({ progress: 100 });

      // Return the image URL
      return imageUrl;
   } catch (error) {
      console.error("Image upload error:", error);

      throw error;
   }
};
