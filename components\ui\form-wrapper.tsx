"use client";

import { useSearchParams } from "next/navigation";
import { ReactNode, Suspense, useEffect, useState } from "react";
import { FormLoadingOverlay } from "./form-loading-overlay";

interface FormWrapperProps {
   children: ReactNode;
   isEditing?: boolean;
   isSubmitting?: boolean;
   isDataLoaded?: boolean;
   loadingMessage?: string;
   editingMessage?: string;
   submittingMessage?: string;
   className?: string;
}

/**
 * A wrapper component for forms that handles loading states
 * Displays a loading overlay when:
 * 1. The form is in edit mode and data is being fetched
 * 2. The form is being submitted
 */
function FormWrapper({
   children,
   isEditing,
   isSubmitting = false,
   isDataLoaded,
   loadingMessage = "Loading...",
   editingMessage = "Loading content...",
   submittingMessage = "Submitting...",
   className,
}: FormWrapperProps) {
   const searchParams = useSearchParams();
   const id = searchParams.get("id");

   // State to track initial loading when in edit mode
   const [isInitialLoading, setIsInitialLoading] = useState(!!id);

   // Automatically set isEditing based on presence of id if not explicitly provided
   const isEditMode = isEditing !== undefined ? isEditing : !!id;

   // Update loading state based on data loaded status
   useEffect(() => {
      if (isEditMode && isDataLoaded) {
         // Only stop showing loading state when data is actually loaded
         setIsInitialLoading(false);
      }
   }, [isEditMode, isDataLoaded]);

   return (
      <div className={`${className || ""}`}>
         {/* Show loading overlay when in edit mode and data is still loading */}
         <FormLoadingOverlay
            isLoading={
               isEditMode && (isInitialLoading || isDataLoaded === false)
            }
            message={editingMessage}
         />

         {/* Show loading overlay when submitting */}
         <FormLoadingOverlay
            isLoading={isSubmitting}
            message={submittingMessage}
         />

         {/* Wrap content in Suspense to handle async loading */}
         <Suspense
            fallback={
               <FormLoadingOverlay isLoading={true} message={loadingMessage} />
            }
         >
            {children}
         </Suspense>
      </div>
   );
}

export { FormWrapper };
