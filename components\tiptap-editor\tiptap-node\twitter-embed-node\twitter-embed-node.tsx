"use client";

import { TrashIcon } from "@/components/tiptap-editor/tiptap-icons/trash-icon";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { NodeViewProps, NodeViewWrapper } from "@tiptap/react";
import * as React from "react";

export const TwitterEmbed: React.FC<NodeViewProps> = (props) => {
   // Function to extract Twitter tweet ID from URL
   const getTweetId = (url: string | null | undefined) => {
      if (!url) return null;

      const regExp = /twitter.com\/[^/]+\/status\/(\d+)/;
      const match = url.match(regExp);

      // Also handle x.com URLs
      const xRegExp = /x.com\/[^/]+\/status\/(\d+)/;
      const xMatch = url.match(xRegExp);

      return match ? match[1] : xMatch ? xMatch[1] : null;
   };

   // Check if we have a valid Twitter URL
   const hasValidSrc = React.useMemo(() => {
      return !!props.node.attrs.src && !!getTweetId(props.node.attrs.src);
   }, [props.node.attrs.src]);

   const [isEditing, setIsEditing] = React.useState(!hasValidSrc);
   const [url, setUrl] = React.useState(props.node.attrs.src || "");
   const [width, setWidth] = React.useState(props.node.attrs.width || "100%");
   const [theme, setTheme] = React.useState(props.node.attrs.theme || "light");
   const [iframeLoaded, setIframeLoaded] = React.useState(false);
   const [error, setError] = React.useState<string | null>(null);

   const tweetId = getTweetId(props.node.attrs.src);

   // Load Twitter widget script if needed
   React.useEffect(() => {
      if (!tweetId || isEditing) return;

      // Only load the script if it's not already loaded
      if (!window.twttr) {
         const script = document.createElement("script");
         script.src = "https://platform.twitter.com/widgets.js";
         script.async = true;
         script.id = "twitter-widget-script";
         document.body.appendChild(script);

         script.onload = () => {
            if (window.twttr) {
               window.twttr.widgets.load();
               setIframeLoaded(true);
            }
         };
      } else if (window.twttr) {
         window.twttr.widgets.load();
         setIframeLoaded(true);
      }
   }, [tweetId, isEditing]);

   const handleSave = (e: React.MouseEvent<HTMLButtonElement>) => {
      // Prevent default to avoid form submission
      e.preventDefault();
      e.stopPropagation();

      // Clear previous errors
      setError(null);

      const tweetId = getTweetId(url);
      if (!tweetId) {
         setError("Please enter a valid Twitter/X URL");
         return;
      }

      props.updateAttributes({
         src: url,
         width,
         theme,
      });
      setIsEditing(false);
      setIframeLoaded(false);
   };

   const handleDelete = () => {
      props.deleteNode();
   };

   return (
      <NodeViewWrapper className="twitter-embed-node">
         {isEditing ? (
            <div className="twitter-embed-form space-y-4 rounded-md border bg-card p-4">
               <div className="flex flex-col gap-2">
                  <Label htmlFor="twitter-url">Twitter/X Tweet URL</Label>
                  <Input
                     id="twitter-url"
                     type="text"
                     placeholder="https://twitter.com/username/status/..."
                     value={url}
                     onChange={(e) => {
                        setUrl(e.target.value);
                        // Clear error when user types
                        if (error) setError(null);
                     }}
                     className={error ? "border-red-500" : ""}
                  />
                  {error && <div className="text-sm text-red-500">{error}</div>}
               </div>

               <div className="flex flex-col gap-2">
                  <Label htmlFor="twitter-width">Width</Label>
                  <Input
                     id="twitter-width"
                     type="text"
                     placeholder="100%"
                     value={width}
                     onChange={(e) => setWidth(e.target.value)}
                  />
               </div>

               <div className="flex flex-col gap-2">
                  <Label htmlFor="twitter-theme">Theme</Label>
                  <select
                     id="twitter-theme"
                     className="flex h-9 w-full rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                     value={theme}
                     onChange={(e) => setTheme(e.target.value)}
                  >
                     <option value="light">Light</option>
                     <option value="dark">Dark</option>
                  </select>
               </div>

               <div className="flex justify-end gap-2">
                  <Button
                     variant="outline"
                     type="button"
                     onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        if (!props.node.attrs.src) {
                           handleDelete();
                        } else {
                           setIsEditing(false);
                           setUrl(props.node.attrs.src);
                           setWidth(props.node.attrs.width);
                           setTheme(props.node.attrs.theme);
                        }
                     }}
                  >
                     Cancel
                  </Button>
                  <Button onClick={handleSave} type="button">
                     Save
                  </Button>
               </div>
            </div>
         ) : (
            <div className="twitter-embed-container overflow-hidden rounded-md border">
               <div className="twitter-embed-toolbar flex justify-between bg-muted p-2">
                  <span className="text-sm font-medium">Twitter/X Tweet</span>
                  <div className="flex gap-2">
                     <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setIsEditing(true)}
                        className="h-8 px-2 text-xs"
                     >
                        Edit
                     </Button>
                     <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleDelete}
                        className="h-8 px-2 text-xs text-destructive"
                     >
                        <TrashIcon className="h-4 w-4" />
                     </Button>
                  </div>
               </div>
               <div className="twitter-embed-iframe-container flex justify-center p-4">
                  {tweetId ? (
                     <div
                        className="twitter-iframe-wrapper"
                        style={{
                           width: props.node.attrs.width,
                           maxWidth: "100%",
                        }}
                     >
                        {!iframeLoaded && (
                           <div className="twitter-embed-placeholder flex h-[300px] items-center justify-center rounded-md bg-muted">
                              Loading tweet...
                           </div>
                        )}
                        <blockquote
                           className="twitter-tweet"
                           data-theme={props.node.attrs.theme}
                           data-width={props.node.attrs.width}
                        >
                           <a href={props.node.attrs.src}></a>
                        </blockquote>
                     </div>
                  ) : (
                     <div className="twitter-embed-placeholder flex h-[300px] w-full max-w-[550px] items-center justify-center rounded-md bg-muted">
                        No Twitter URL provided
                     </div>
                  )}
               </div>
            </div>
         )}
      </NodeViewWrapper>
   );
};

// Add TypeScript interface for the Twitter widget
declare global {
   interface Window {
      twttr?: {
         widgets: {
            load: () => void;
         };
      };
   }
}
