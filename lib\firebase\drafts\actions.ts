"use server";

import { uploadImage } from "@/lib/cloudinary/cloudinary";
import { slugify } from "@/lib/utils";
import {
   addDoc,
   collection,
   deleteDoc,
   doc,
   getDoc,
   getDocs,
   query,
   Timestamp,
   updateDoc,
   where,
} from "firebase/firestore";
import { revalidatePath } from "next/cache";
import { db } from "../firebase";
import { createPost } from "../posts/actions";

export type DraftFormType = {
   title: string;
   summary?: string;
   categoryId?: string;
   authorId?: string;
   poster?: File | string;
   tags?: string[];
   content?: string;
   sections?: string[];
};

export async function createDraft(data: DraftFormType) {
   const { title, poster, sections } = data;

   // Generate a slug from the draft title
   const slug = slugify(title);

   // Check if a draft with the same title already exists
   const draftsRef = collection(db, "drafts");
   const q = query(draftsRef, where("title", "==", title));
   const querySnapshot = await getDocs(q);
   if (!querySnapshot.empty) {
      return {
         message: "A draft with the same title already exists",
         success: false,
      };
   }

   // Generate a unique ID for the new draft document
   const uniqueId = doc(collection(db, "drafts")).id;

   // Upload image if provided
   let imageUrl = "";
   if (poster instanceof File) {
      imageUrl = await uploadImage(poster, uniqueId);
      if (!imageUrl)
         return {
            message: "Image upload failed, please try again!",
            success: false,
         };
   } else if (typeof poster === "string" && poster) {
      imageUrl = poster;
   }

   const newDraft = {
      ...data,
      title,
      slug,
      ...(imageUrl && { poster: imageUrl }),
      tags: data.tags || [],
      sections: sections || [],
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
   };

   try {
      const docRef = await addDoc(collection(db, "drafts"), newDraft);

      if (!docRef) {
         return {
            message: "Failed to create draft",
            success: false,
         };
      }

      revalidatePath("/posts/drafts");

      return {
         message: "Draft created successfully",
         success: true,
         draftId: docRef.id,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to create draft",
         success: false,
      };
   }
}

export async function updateDraft(data: DraftFormType, id: string) {
   const { title, poster, sections } = data;

   // Generate a slug from the draft title
   const slug = slugify(title);

   // Check if a draft with the same title already exists
   const draftSnap = await getDoc(doc(db, "drafts", id));
   if (!draftSnap.exists()) {
      return {
         message: "Draft not found",
         success: false,
      };
   }

   if (!(draftSnap.data().title === title)) {
      const draftsRef = collection(db, "drafts");
      const q = query(draftsRef, where("title", "==", title));
      const querySnapshot = await getDocs(q);
      if (!querySnapshot.empty) {
         return {
            message: "A draft with the same title already exists",
            success: false,
         };
      }
   }

   // Upload image if provided
   let imageUrl = "";
   if (poster instanceof File) {
      imageUrl = await uploadImage(poster, id);
      if (!imageUrl)
         return {
            message: "Image upload failed, please try again!",
            success: false,
         };
   } else if (typeof poster === "string" && poster) {
      imageUrl = poster;
   }

   const docRef = doc(db, "drafts", id);

   try {
      await updateDoc(docRef, {
         ...data,
         title,
         slug,
         ...(imageUrl && { poster: imageUrl }),
         sections: sections || [],
         updatedAt: Timestamp.now(),
      });

      revalidatePath("/posts/drafts");

      return {
         message: "Draft updated successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to update draft",
         success: false,
      };
   }
}

export async function deleteDraft(id: string) {
   if (!id) {
      return {
         message: "Draft ID is required",
         success: false,
      };
   }

   try {
      const docRef = doc(db, "drafts", id);
      await deleteDoc(docRef);
      revalidatePath("/posts/drafts");

      return {
         message: "Draft deleted successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to delete draft",
         success: false,
      };
   }
}

export async function publishDraft(id: string) {
   // Get the draft data
   const draftRef = doc(db, "drafts", id);
   const draftSnap = await getDoc(draftRef);

   if (!draftSnap.exists()) {
      return {
         message: "Draft not found",
         success: false,
      };
   }

   const draftData = draftSnap.data();

   // Validate required fields for publishing
   if (!draftData.title) {
      return {
         message: "Title is required",
         success: false,
      };
   }
   if (!draftData.summary) {
      return {
         message: "Summary is required",
         success: false,
      };
   }
   if (!draftData.categoryId) {
      return {
         message: "Category is required",
         success: false,
      };
   }
   if (!draftData.authorId) {
      return {
         message: "Author is required",
         success: false,
      };
   }
   if (!draftData.content) {
      return {
         message: "Content is required",
         success: false,
      };
   }
   if (!draftData.poster) {
      return {
         message: "Featured image is required",
         success: false,
      };
   }

   try {
      // Create a post from the draft
      const result = await createPost({
         title: draftData.title,
         summary: draftData.summary,
         categoryId: draftData.categoryId,
         authorId: draftData.authorId,
         poster: draftData.poster, // This is a string URL from the draft
         tags: draftData.tags || [],
         content: draftData.content,
         sections: draftData.sections || [],
      });

      if (!result.success) {
         return result;
      }

      // Delete the draft
      const deleteResult = await deleteDraft(id);
      if (!deleteResult.success) {
         return {
            message: "Post created but failed to delete draft",
            success: true, // Still consider it a success since the post was created
         };
      }

      revalidatePath("/posts");

      return {
         message: "Draft published successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error ? error.message : "Failed to publish draft",
         success: false,
      };
   }
}

export async function convertPostToDraft(postId: string) {
   // Get the post data
   const postRef = doc(db, "posts", postId);
   const postSnap = await getDoc(postRef);

   if (!postSnap.exists()) {
      return {
         message: "Post not found",
         success: false,
      };
   }

   const postData = postSnap.data();

   try {
      // Create a draft from the post
      const draftsRef = collection(db, "drafts");
      await addDoc(draftsRef, {
         ...postData,
         updatedAt: Timestamp.now(),
      });

      // Delete the post
      await deleteDoc(postRef);

      revalidatePath("/posts/drafts");

      return {
         message: "Post converted to draft successfully",
         success: true,
      };
   } catch (error) {
      return {
         message:
            error instanceof Error
               ? error.message
               : "Failed to convert post to draft",
         success: false,
      };
   }
}
