"use client";

import { type Editor } from "@tiptap/react";
import * as React from "react";

// --- Icons ---
import { MaximizeIcon } from "@/components/tiptap-editor/tiptap-icons/maximize-icon";
import { MinimizeIcon } from "@/components/tiptap-editor/tiptap-icons/minimize-icon";

// --- UI Primitives ---
import {
   Button,
   ButtonProps,
} from "@/components/tiptap-editor/tiptap-ui-primitive/button";

export interface FullscreenButtonProps extends ButtonProps {
   /**
    * The TipTap editor instance.
    */
   editor?: Editor | null;
   /**
    * Whether the editor is in fullscreen mode.
    */
   isFullscreen: boolean;
   /**
    * Function to toggle fullscreen mode.
    */
   onToggleFullscreen: () => void;
   /**
    * Optional text to display alongside the icon.
    */
   text?: string;
}

export const fullscreenShortcutKey = "F11";

export const FullscreenButton = ({
   isFullscreen,
   onToggleFullscreen,
   text,
   ...props
}: FullscreenButtonProps) => {
   const Icon = isFullscreen ? MinimizeIcon : MaximizeIcon;
   const label = isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen";

   return (
      <Button
         tooltip={label}
         shortcutKeys={fullscreenShortcutKey}
         onClick={onToggleFullscreen}
         aria-label={label}
         type="button"
         {...props}
      >
         <Icon className="tiptap-button-icon" />
         {text && <span>{text}</span>}
      </Button>
   );
};

export default FullscreenButton;
