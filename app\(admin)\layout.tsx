import { auth } from "@/auth";
import { AppHeader } from "@/components/app-header";
import { AppSidebar } from "@/components/app-sidebar";
import RequireAuth from "@/components/auth/require-auth";
import { SidebarProvider } from "@/components/ui/sidebar";
import { redirect } from "next/navigation";

async function Layout({ children }: { children: React.ReactNode }) {
   const session = await auth();
   const requiredRoles = ["admin", "editor"];

   // Server-side authentication check
   if (!session) {
      redirect("/");
   }

   // Server-side role check
   if (!requiredRoles.includes(session.user.role as string)) {
      redirect("/");
   }

   return (
      // Client-side authentication and role check
      <SidebarProvider>
         <div className="flex min-h-svh max-w-full grow flex-col md:flex-row">
            <AppSidebar />
            <div className="flex w-full max-w-full flex-col overflow-hidden">
               <AppHeader />
               <RequireAuth requiredRoles={requiredRoles}>
                  <main className="max-w-full grow overflow-auto p-6 pt-[5.5rem]">
                     {children}
                  </main>
               </RequireAuth>
            </div>
         </div>
      </SidebarProvider>
   );
}
export default Layout;
